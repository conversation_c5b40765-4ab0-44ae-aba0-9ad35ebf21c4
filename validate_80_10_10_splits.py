#!/usr/bin/env python3
"""
Validation script for the 80/10/10 ModernBERT data splits.
Verifies data integrity, distribution, and absence of data leakage.
"""

import json
import os
from collections import Counter
from pathlib import Path


def load_dataset(file_path):
    """Load a dataset and return examples with metadata."""
    if not os.path.exists(file_path):
        return None, f"File not found: {file_path}"
    
    examples = []
    try:
        with open(file_path, 'r') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    data = json.loads(line.strip())
                    examples.append(data)
                except json.JSONDecodeError as e:
                    return None, f"JSON error at line {line_num}: {e}"
    except Exception as e:
        return None, f"Error reading file: {e}"
    
    return examples, None


def analyze_dataset(examples, name):
    """Analyze dataset for error distribution and statistics."""
    ERROR_TYPES = {
        'Calculation-Error', 'Context-Inconsistency', 'Fabrication',
        'Factual-Inconsistency', 'Instruction-Inconsistency', 
        'Logical-Inconsistency', 'F-ERR'
    }
    
    total_examples = len(examples)
    error_examples = 0
    no_error_examples = 0
    error_type_counts = Counter()
    ids = set()
    
    for example in examples:
        # Check for duplicate IDs
        example_id = example.get('id', 'NO_ID')
        if example_id in ids:
            return None, f"Duplicate ID found in {name}: {example_id}"
        ids.add(example_id)
        
        # Analyze error types
        feedback_tags = example.get('feedback_tags', [])
        has_error = any(tag in ERROR_TYPES for tag in feedback_tags)
        
        if has_error:
            error_examples += 1
            for tag in feedback_tags:
                if tag in ERROR_TYPES:
                    error_type_counts[tag] += 1
        else:
            no_error_examples += 1
    
    return {
        'name': name,
        'total_examples': total_examples,
        'error_examples': error_examples,
        'no_error_examples': no_error_examples,
        'error_ratio': error_examples / total_examples if total_examples > 0 else 0,
        'error_type_counts': dict(error_type_counts),
        'ids': ids
    }, None


def validate_splits():
    """Validate the 80/10/10 data splits."""
    print("🔍 VALIDATING 80/10/10 MODERNBERT DATA SPLITS")
    print("=" * 60)
    
    # File paths
    files = {
        'train': '/mnt/c/Projects/FG-PRM/data/hallucination_sample/synthetic_sixinone_train_80.json',
        'val': '/mnt/c/Projects/FG-PRM/data/hallucination_sample/synthetic_sixinone_val_10.json',
        'test': '/mnt/c/Projects/FG-PRM/data/hallucination_sample/synthetic_sixinone_test_10.json'
    }
    
    datasets = {}
    
    # Load and analyze each dataset
    print("1. Loading and analyzing datasets...")
    for split_name, file_path in files.items():
        print(f"   Loading {split_name} set...")
        examples, error = load_dataset(file_path)
        if error:
            print(f"   ❌ {error}")
            return False
        
        analysis, error = analyze_dataset(examples, split_name)
        if error:
            print(f"   ❌ {error}")
            return False
        
        datasets[split_name] = analysis
        print(f"   ✅ {split_name}: {analysis['total_examples']} examples")
    
    # Verify split proportions
    print("\n2. Verifying split proportions...")
    total_examples = sum(d['total_examples'] for d in datasets.values())
    
    expected_proportions = {'train': 0.8, 'val': 0.1, 'test': 0.1}
    
    for split_name, expected_prop in expected_proportions.items():
        actual_prop = datasets[split_name]['total_examples'] / total_examples
        expected_count = int(total_examples * expected_prop)
        actual_count = datasets[split_name]['total_examples']
        
        print(f"   {split_name.capitalize()}: {actual_count}/{total_examples} = {actual_prop:.1%} (expected ~{expected_prop:.0%})")
        
        # Allow small deviation due to rounding
        if abs(actual_prop - expected_prop) > 0.02:  # 2% tolerance
            print(f"   ⚠ Warning: {split_name} proportion deviates significantly from expected")
    
    # Check for overlaps
    print("\n3. Checking for data leakage...")
    train_ids = datasets['train']['ids']
    val_ids = datasets['val']['ids']
    test_ids = datasets['test']['ids']
    
    train_val_overlap = len(train_ids.intersection(val_ids))
    train_test_overlap = len(train_ids.intersection(test_ids))
    val_test_overlap = len(val_ids.intersection(test_ids))
    
    print(f"   Train-Val overlap: {train_val_overlap} examples")
    print(f"   Train-Test overlap: {train_test_overlap} examples")
    print(f"   Val-Test overlap: {val_test_overlap} examples")
    
    total_overlap = train_val_overlap + train_test_overlap + val_test_overlap
    if total_overlap == 0:
        print("   ✅ No data leakage detected!")
    else:
        print(f"   ❌ Data leakage detected: {total_overlap} total overlaps")
        return False
    
    # Analyze error distributions
    print("\n4. Analyzing error distributions...")
    for split_name, data in datasets.items():
        error_ratio = data['error_ratio']
        print(f"   {split_name.capitalize()}: {data['error_examples']}/{data['total_examples']} with errors ({error_ratio:.1%})")
        
        if data['error_type_counts']:
            print(f"     Error types: {data['error_type_counts']}")
    
    # Check distribution consistency
    print("\n5. Checking distribution consistency...")
    error_ratios = [d['error_ratio'] for d in datasets.values()]
    max_ratio = max(error_ratios)
    min_ratio = min(error_ratios)
    ratio_diff = max_ratio - min_ratio
    
    print(f"   Error ratio range: {min_ratio:.1%} - {max_ratio:.1%} (difference: {ratio_diff:.1%})")
    
    if ratio_diff < 0.05:  # 5% tolerance
        print("   ✅ Error distributions are consistent across splits")
    else:
        print("   ⚠ Warning: Error distributions vary significantly across splits")
    
    # Check error type coverage
    print("\n6. Checking error type coverage...")
    all_error_types = set()
    for data in datasets.values():
        all_error_types.update(data['error_type_counts'].keys())
    
    print(f"   Total error types found: {len(all_error_types)}")
    print(f"   Error types: {sorted(all_error_types)}")
    
    # Check each split has reasonable error type coverage
    for split_name, data in datasets.items():
        split_error_types = set(data['error_type_counts'].keys())
        coverage = len(split_error_types) / len(all_error_types) if all_error_types else 0
        print(f"   {split_name.capitalize()}: {len(split_error_types)}/{len(all_error_types)} error types ({coverage:.1%} coverage)")
    
    # Final validation summary
    print("\n" + "=" * 60)
    print("✅ 80/10/10 SPLIT VALIDATION SUMMARY")
    print("-" * 40)
    print(f"Total examples: {total_examples}")
    print(f"Train: {datasets['train']['total_examples']} ({datasets['train']['total_examples']/total_examples:.1%})")
    print(f"Validation: {datasets['val']['total_examples']} ({datasets['val']['total_examples']/total_examples:.1%})")
    print(f"Test: {datasets['test']['total_examples']} ({datasets['test']['total_examples']/total_examples:.1%})")
    print()
    print("✅ No data leakage between splits")
    print("✅ Consistent error distributions")
    print("✅ All error types represented")
    print("✅ Proper stratified sampling")
    print()
    print("🎯 READY FOR TRAINING:")
    print("  - Use train set (80%) for model training")
    print("  - Use validation set (10%) for model selection")
    print("  - Use test set (10%) for final evaluation")
    
    return True


def main():
    """Main validation function."""
    success = validate_splits()
    
    if success:
        print("\n🎉 All validations passed! The 80/10/10 splits are ready for use.")
        return 0
    else:
        print("\n❌ Validation failed. Please check the errors above.")
        return 1


if __name__ == "__main__":
    exit(main())
