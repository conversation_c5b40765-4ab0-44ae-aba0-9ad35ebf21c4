#!/usr/bin/env python3
"""
Verification script for ModernBERT-based Fine-Grained Process Reward Models.

This script loads the 6 trained PRMs and uses them to verify solutions by:
1. Loading all 6 binary PRMs (one for each error type)
2. Processing solutions with the "super-evaluator" that aggregates scores
3. Selecting the best candidate based on aggregated scores
4. Comparing with correct answers to calculate verification accuracy

Compatible with existing verification pipeline and aggregation code.
"""

import argparse
import json
import logging
import os
import sys
from typing import Dict, List, Optional, Tuple, Any
import numpy as np
import torch
from transformers import AutoTokenizer
import spacy

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fine_tuning.my_modernbert import ModernBertForTokenClassification
from fine_tuning.reward_modernbert import ModernBertFineGrainedReward
from verification.reward import FineGrainedReward

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Error type mappings
ERROR_TYPES = [
    "Calculation-Error",
    "Fabrication", 
    "Context-Inconsistency",
    "Factual-Inconsistency",
    "Instruction-Inconsistency",
    "Logical-Inconsistency"
]


class ModernBertVerifier:
    """Verifier using 6 separate ModernBERT PRMs for fine-grained error detection."""
    
    def __init__(self, model_base_dir: str, device: str = "cuda"):
        """
        Initialize the verifier with trained ModernBERT models.
        
        Args:
            model_base_dir: Base directory containing the 6 trained PRM models
            device: Device to run models on
        """
        self.device = device
        self.model_base_dir = model_base_dir
        self.models = {}
        self.tokenizers = {}
        
        # Load spacy for text processing
        self.nlp = spacy.load("en_core_web_sm")
        
        # Load all 6 models
        self._load_models()
        
        logger.info(f"Loaded {len(self.models)} ModernBERT PRMs")
    
    def _load_models(self):
        """Load all 6 trained PRM models."""
        for error_type in ERROR_TYPES:
            model_dir = os.path.join(self.model_base_dir, error_type.replace("-", "_").lower())
            
            if not os.path.exists(model_dir):
                logger.warning(f"Model directory not found: {model_dir}")
                continue
            
            try:
                # Load tokenizer
                tokenizer = AutoTokenizer.from_pretrained(model_dir)
                
                # Load model
                model = ModernBertForTokenClassification.from_pretrained(model_dir)
                model.to(self.device)
                model.eval()
                
                self.tokenizers[error_type] = tokenizer
                self.models[error_type] = model
                
                logger.info(f"Loaded {error_type} PRM from {model_dir}")
                
            except Exception as e:
                logger.error(f"Failed to load {error_type} PRM: {str(e)}")
    
    def _process_solution_text(self, question: str, solution: str) -> str:
        """Process solution text into the format expected by PRMs."""
        # Split solution into sentences
        sentences = []
        doc = self.nlp(solution)
        for sent in doc.sents:
            sentences.append(sent.text.strip())
        
        # Format with [SEP] tokens
        formatted_sentences = [f"{sent} [SEP]" for sent in sentences]
        formatted_solution = " ".join(formatted_sentences)
        
        # Combine with question
        full_input = f"{question} answer: {formatted_solution}"
        
        return full_input
    
    def _get_prm_scores(self, input_text: str, error_type: str) -> List[float]:
        """Get PRM scores for a specific error type."""
        if error_type not in self.models:
            logger.warning(f"Model not available for {error_type}")
            return []
        
        tokenizer = self.tokenizers[error_type]
        model = self.models[error_type]
        
        # Tokenize input
        inputs = tokenizer(
            input_text.split(),
            return_tensors="pt",
            is_split_into_words=True,
            truncation=True,
            padding=True
        )
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        
        # Get model predictions
        with torch.no_grad():
            outputs = model(**inputs)
            logits = outputs.logits[0]  # Remove batch dimension
        
        # Find [SEP] token positions
        sep_token_id = tokenizer.convert_tokens_to_ids("[SEP]")
        input_ids = inputs["input_ids"][0]
        sep_positions = torch.where(input_ids == sep_token_id)[0]
        
        # Extract scores at [SEP] positions
        scores = []
        for pos in sep_positions:
            # Get probability of "no error" (class 1)
            step_logits = logits[pos]
            step_probs = torch.softmax(step_logits, dim=-1)
            no_error_prob = step_probs[1].item()  # Assuming class 1 is "no error"
            scores.append(no_error_prob)
        
        return scores
    
    def verify_solution(self, question: str, solution: str) -> Dict[str, Any]:
        """
        Verify a solution using all 6 PRMs.
        
        Args:
            question: The question text
            solution: The solution text to verify
            
        Returns:
            Dictionary containing verification results
        """
        # Process input text
        input_text = self._process_solution_text(question, solution)
        
        # Get scores from all PRMs
        all_scores = {}
        aggregated_scores = []
        
        for error_type in ERROR_TYPES:
            scores = self._get_prm_scores(input_text, error_type)
            all_scores[error_type] = scores
            
            if not aggregated_scores:
                aggregated_scores = scores.copy()
            else:
                # Sum scores across error types (as specified in requirements)
                for i in range(min(len(aggregated_scores), len(scores))):
                    aggregated_scores[i] += scores[i]
        
        # Calculate overall score (sum across steps)
        overall_score = sum(aggregated_scores) if aggregated_scores else 0.0
        
        # Calculate step-wise average
        avg_step_score = overall_score / len(aggregated_scores) if aggregated_scores else 0.0
        
        return {
            "overall_score": overall_score,
            "avg_step_score": avg_step_score,
            "step_scores": aggregated_scores,
            "error_type_scores": all_scores,
            "num_steps": len(aggregated_scores)
        }
    
    def verify_candidates(self, question: str, candidates: List[str]) -> Tuple[int, List[Dict[str, Any]]]:
        """
        Verify multiple solution candidates and select the best one.
        
        Args:
            question: The question text
            candidates: List of candidate solutions
            
        Returns:
            Tuple of (best_candidate_index, list_of_verification_results)
        """
        results = []
        
        for candidate in candidates:
            result = self.verify_solution(question, candidate)
            results.append(result)
        
        # Select best candidate based on overall score
        best_idx = 0
        best_score = results[0]["overall_score"]
        
        for i, result in enumerate(results[1:], 1):
            if result["overall_score"] > best_score:
                best_score = result["overall_score"]
                best_idx = i
        
        return best_idx, results


def load_test_data(test_file: str) -> List[Dict[str, Any]]:
    """Load test data from file."""
    test_data = []
    
    with open(test_file, 'r') as f:
        if test_file.endswith('.jsonl'):
            for line in f:
                test_data.append(json.loads(line))
        else:
            test_data = json.load(f)
    
    return test_data


def calculate_verification_accuracy(verifier: ModernBertVerifier, test_data: List[Dict[str, Any]]) -> Dict[str, float]:
    """Calculate verification accuracy metrics."""
    correct_selections = 0
    total_questions = 0
    
    all_scores = []
    correct_answer_scores = []
    
    for item in test_data:
        question = item.get("question", "")
        candidates = item.get("candidates", [])
        correct_idx = item.get("correct_answer_idx", 0)
        
        if not candidates:
            continue
        
        # Verify all candidates
        selected_idx, results = verifier.verify_candidates(question, candidates)
        
        # Check if selection is correct
        if selected_idx == correct_idx:
            correct_selections += 1
        
        total_questions += 1
        
        # Collect scores for analysis
        for result in results:
            all_scores.append(result["overall_score"])
        
        if correct_idx < len(results):
            correct_answer_scores.append(results[correct_idx]["overall_score"])
    
    accuracy = correct_selections / total_questions if total_questions > 0 else 0.0
    
    return {
        "verification_accuracy": accuracy,
        "correct_selections": correct_selections,
        "total_questions": total_questions,
        "avg_score": np.mean(all_scores) if all_scores else 0.0,
        "avg_correct_score": np.mean(correct_answer_scores) if correct_answer_scores else 0.0
    }


def main():
    """Main function for running verification."""
    parser = argparse.ArgumentParser(description="Verify solutions using ModernBERT PRMs")
    parser.add_argument("--model_base_dir", type=str, required=True,
                        help="Base directory containing trained PRM models")
    parser.add_argument("--test_file", type=str, required=True,
                        help="Test data file (JSON or JSONL)")
    parser.add_argument("--output_file", type=str, default="verification_results.json",
                        help="Output file for results")
    parser.add_argument("--device", type=str, default="cuda",
                        help="Device to run models on")
    parser.add_argument("--verbose", action="store_true",
                        help="Enable verbose logging")

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Check if model directory exists
    if not os.path.exists(args.model_base_dir):
        logger.error(f"Model directory not found: {args.model_base_dir}")
        sys.exit(1)

    # Check if test file exists
    if not os.path.exists(args.test_file):
        logger.error(f"Test file not found: {args.test_file}")
        sys.exit(1)

    # Initialize verifier
    logger.info("Initializing ModernBERT verifier...")
    verifier = ModernBertVerifier(args.model_base_dir, args.device)

    # Load test data
    logger.info(f"Loading test data from {args.test_file}")
    test_data = load_test_data(args.test_file)
    logger.info(f"Loaded {len(test_data)} test examples")

    # Run verification
    logger.info("Running verification...")
    results = calculate_verification_accuracy(verifier, test_data)

    # Print results
    print("\n" + "="*50)
    print("VERIFICATION RESULTS")
    print("="*50)
    print(f"Verification Accuracy: {results['verification_accuracy']:.4f}")
    print(f"Correct Selections: {results['correct_selections']}/{results['total_questions']}")
    print(f"Average Score: {results['avg_score']:.4f}")
    print(f"Average Correct Answer Score: {results['avg_correct_score']:.4f}")
    print("="*50)

    # Save results
    with open(args.output_file, 'w') as f:
        json.dump(results, f, indent=2)

    logger.info(f"Results saved to {args.output_file}")


if __name__ == "__main__":
    main()
