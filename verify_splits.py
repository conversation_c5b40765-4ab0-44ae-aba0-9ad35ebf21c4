#!/usr/bin/env python3
"""
Simple verification script for the created 80/10/10 splits.
"""

import json
import os
from collections import Counter

def load_json_lines(file_path):
    """Load JSON lines file and return list of entries."""
    entries = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if not line:
                continue
            try:
                entry = json.loads(line)
                entries.append(entry)
            except json.JSONDecodeError as e:
                print(f"Error parsing JSON at line {line_num} in {file_path}: {e}")
                continue
    return entries

def main():
    """Main verification function."""
    
    base_dir = "data/hallucination_sample"
    
    # Define split files
    split_files = {
        'Training (80%)': os.path.join(base_dir, "synthetic_AllErr_orm_train_80.json"),
        'Validation (10%)': os.path.join(base_dir, "synthetic_AllErr_orm_val_10.json"),
        'Test (10%)': os.path.join(base_dir, "synthetic_AllErr_orm_test_10.json")
    }
    
    print("=== 80/10/10 Split Verification ===\n")
    
    # Load all splits
    splits_data = {}
    total_entries = 0
    
    for split_name, file_path in split_files.items():
        if not os.path.exists(file_path):
            print(f"ERROR: File {file_path} does not exist!")
            continue
            
        entries = load_json_lines(file_path)
        splits_data[split_name] = entries
        total_entries += len(entries)
        
        # Get file size
        size_bytes = os.path.getsize(file_path)
        if size_bytes < 1024 * 1024:
            size_str = f"{size_bytes / 1024:.1f} KB"
        else:
            size_str = f"{size_bytes / (1024 * 1024):.1f} MB"
        
        print(f"{split_name}: {len(entries)} entries ({size_str})")
    
    print(f"\nTotal entries across all splits: {total_entries}")
    
    # Calculate exact percentages
    if total_entries > 0:
        train_entries = len(splits_data.get('Training (80%)', []))
        val_entries = len(splits_data.get('Validation (10%)', []))
        test_entries = len(splits_data.get('Test (10%)', []))
        
        train_pct = train_entries / total_entries * 100
        val_pct = val_entries / total_entries * 100
        test_pct = test_entries / total_entries * 100
        
        print(f"\nActual split percentages:")
        print(f"  Training: {train_pct:.2f}% (target: 80%)")
        print(f"  Validation: {val_pct:.2f}% (target: 10%)")
        print(f"  Test: {test_pct:.2f}% (target: 10%)")
    
    # Check for ID overlaps
    print(f"\n=== Data Integrity Check ===")
    train_ids = set(entry['id'] for entry in splits_data.get('Training (80%)', []))
    val_ids = set(entry['id'] for entry in splits_data.get('Validation (10%)', []))
    test_ids = set(entry['id'] for entry in splits_data.get('Test (10%)', []))
    
    train_val_overlap = train_ids & val_ids
    train_test_overlap = train_ids & test_ids
    val_test_overlap = val_ids & test_ids
    
    if not train_val_overlap and not train_test_overlap and not val_test_overlap:
        print("✓ No overlapping IDs between splits - data integrity confirmed")
    else:
        print("❌ Found overlapping IDs:")
        if train_val_overlap:
            print(f"  Train-Val overlap: {len(train_val_overlap)} IDs")
        if train_test_overlap:
            print(f"  Train-Test overlap: {len(train_test_overlap)} IDs")
        if val_test_overlap:
            print(f"  Val-Test overlap: {len(val_test_overlap)} IDs")
    
    # Analyze feedback tag distribution
    print(f"\n=== Feedback Tag Distribution ===")
    for split_name, entries in splits_data.items():
        if not entries:
            continue
            
        tag_patterns = Counter()
        for entry in entries:
            feedback_tags = entry.get('feedback_tags', [])
            unique_tags = set(feedback_tags)
            
            if 'F-ERR' in unique_tags:
                tag_patterns['F-ERR'] += 1
            elif 'O' in unique_tags and len(unique_tags) == 2:  # O and Ignore
                tag_patterns['O_only'] += 1
            elif unique_tags == {'Ignore'}:
                tag_patterns['Ignore_only'] += 1
            else:
                tag_patterns['Mixed'] += 1
        
        print(f"\n{split_name}:")
        total = len(entries)
        for pattern, count in tag_patterns.items():
            percentage = (count / total) * 100 if total > 0 else 0
            print(f"  {pattern}: {count} ({percentage:.1f}%)")
    
    print(f"\n✅ Verification completed!")
    print(f"\nFiles created:")
    for split_name, file_path in split_files.items():
        if os.path.exists(file_path):
            print(f"  - {os.path.basename(file_path)}")

if __name__ == "__main__":
    main()
