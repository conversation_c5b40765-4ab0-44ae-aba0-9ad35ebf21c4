# ModernBERT Binary Classification Training Pipeline

This directory contains a complete training pipeline for ModernBERT-large with binary classification, designed for comprehensive multi-error reward modeling using the sixinone dataset.

## Overview

The ModernBERT pipeline provides binary sequence classification for reward modeling tasks, converting token-level multi-error detection to sequence-level binary classification (0 = no error, 1 = has any type of error).

### Comprehensive Multi-Error Support

The pipeline handles all 6 error types from the sixinone dataset:
- **Calculation-Error**: Mathematical computation errors
- **Context-Inconsistency**: Information inconsistent with context
- **Fabrication**: Made-up or false information
- **Factual-Inconsistency**: Incorrect factual claims
- **Instruction-Inconsistency**: Response doesn't follow instructions
- **Logical-Inconsistency**: Logical reasoning errors

### Dataset Statistics (Proper 80/10/10 Splits)
- **Training**: 460 examples (78.3% with errors) - 80% of total data
- **Validation**: 57 examples (77.2% with errors) - 10% for model selection
- **Test**: 59 examples (81.4% with errors) - 10% for final evaluation
- **Total Unique Examples**: 576 (after deduplication from original datasets)
- **Data Integrity**: ✅ No overlap between train/validation/test splits
- **Balanced Distribution**: All 6 error types represented across all splits
- **Comprehensive Coverage**: Single dataset covers all error scenarios

### Data Leakage Prevention & Proper Evaluation
- **Original Issue**: Previous datasets had massive duplication and overlap
- **Solution**: Proper 80/10/10 split with stratified sampling
- **Training Set**: 80% for model training
- **Validation Set**: 10% for model selection and hyperparameter tuning
- **Test Set**: 10% for final unbiased evaluation (never seen during training)
- **Quality**: Stratified sampling maintains error distribution across all splits

### Key Features

- **Binary Classification**: Converts token-level feedback to sequence-level binary labels
- **ModernBERT-large**: Uses the efficient 395M parameter ModernBERT-large model
- **Long Context**: Supports up to 8192 tokens (4096 default for efficiency)
- **Efficient Training**: Optimized batch sizes and learning rates for ModernBERT
- **Compatible Structure**: Follows the same conventions as existing Longformer scripts

## Files

### Core Training Scripts
- `rpe_train_fact_rm_modernbert.sh` - Main training shell script
- `run_fg_rm_modernbert.py` - Python training script with binary classification logic
- `eval_binary.py` - Binary classification evaluation metrics
- `convert_to_binary.py` - Utility to convert token-level data to binary format

### Configuration
- Uses `answerdotai/ModernBERT-large` as the base model
- Binary classification with 2 labels (0, 1)
- Maximum sequence length: 4096 tokens (configurable up to 8192)
- Batch size: 8 per device (adjustable based on GPU memory)

## Usage

### 1. Comprehensive Multi-Error Training (Recommended)

Run the main training script with the proper 80/10/10 sixinone dataset splits:

```bash
# Set GPU devices
export CUDA_VISIBLE_DEVICES=0,1,2,3

# Run comprehensive multi-error training with proper data splits
cd src
bash reward_modeling/rpe_train_fact_rm_modernbert.sh
```

This trains on all 6 error types simultaneously using:
- **Training**: 460 examples (80%) for model training
- **Validation**: 57 examples (10%) for model selection
- **Test**: 59 examples (10%) for final evaluation

### 2. Custom Multi-Error Training

Run directly with custom parameters using proper 80/10/10 splits:

```bash
cd src
torchrun --nproc_per_node 4 --standalone --nnodes=1 ./reward_modeling/run_fg_rm_modernbert.py \
    --model_name_or_path answerdotai/ModernBERT-large \
    --train_file ../data/hallucination_sample/synthetic_sixinone_train_80.json \
    --validation_file ../data/hallucination_sample/synthetic_sixinone_val_10.json \
    --test_file ../data/hallucination_sample/synthetic_sixinone_test_10.json \
    --output_dir ../models/orm_modernbert_large_80_10_10 \
    --do_train \
    --do_eval \
    --do_predict \
    --bf16 \
    --num_train_epochs 30 \
    --per_device_train_batch_size 4 \
    --per_device_eval_batch_size 4 \
    --evaluation_strategy epoch \
    --logging_strategy epoch \
    --save_strategy epoch \
    --load_best_model_at_end \
    --metric_for_best_model f1 \
    --max_seq_length 4096 \
    --report_to wandb \
    --save_total_limit 3 \
    --learning_rate 0.00001 \
    --weight_decay 0.001 \
    --warmup_ratio 0.1 \
    --gradient_accumulation_steps 2
```

### 3. Proper Evaluation (No Data Leakage)

After training, evaluate the model using the independent test set (10% split):

```bash
# Evaluate on independent test set (no data leakage)
python reward_modeling/evaluate_80_10_10_splits.py \
    ../models/orm_modernbert_large_sixinone_80_10_10/predictions.txt \
    ../data/hallucination_sample/synthetic_sixinone_test_10.json
```

**Critical**: Always use the test set for final evaluation, never the validation set used during training.

### 4. Data Validation

Verify the integrity of your data splits:

After training, evaluate the model using the independent test set:

```bash
# Evaluate on independent test set (no data leakage)
python reward_modeling/evaluate_clean_splits.py \
    ../models/orm_modernbert_large_sixinone_clean/predictions.txt \
    ../data/hallucination_sample/synthetic_sixinone_test.json
```

**Important**: Always use the test set for final evaluation, not the validation set used during training.

### 4. Data Conversion (Optional)

If you need to convert existing token-level data to binary format:

```bash
cd src/reward_modeling

# Convert single file
python convert_to_binary.py \
    ../../data/hallucination_sample/synthetic_Calculation-Error_orm_train.json \
    ../../data/hallucination_sample/binary_Calculation-Error_orm_train.json

# Convert multiple files
python convert_to_binary.py \
    "../../data/hallucination_sample/*_orm_*.json" \
    ../../data/hallucination_sample/binary/ \
    --multiple
```

### 4. Evaluation

Evaluate binary classification results:

```bash
cd src/reward_modeling

python eval_binary.py \
    ../../models/orm_modernbert_large_Calculation-Error/predictions.txt \
    ../../data/hallucination_sample/synthetic_Calculation-Error_orm_dev.json
```

## Data Format

### Input Format (Token-level)
The pipeline expects JSON files with token-level data:

```json
{
    "id": "example_id",
    "text": ["token1", "token2", "token3", ...],
    "feedback_tags": ["O", "O", "F-ERR", ...]
}
```

### Internal Conversion
The pipeline automatically converts this to binary classification:
- Joins tokens back to text: `"token1 token2 token3 ..."`
- Converts labels: `1` if any tag is not "O" or "Ignore", else `0`

### Output Format
Predictions are saved as:
```
0	0
1	1
2	0
...
```

## Model Architecture

### ModernBERT-large Specifications
- **Parameters**: 395M
- **Hidden Size**: 1024
- **Layers**: 28
- **Attention**: Alternating global/local attention
- **Context Length**: 8192 tokens (native)
- **Efficiency**: 2-3x faster than comparable models

### Training Configuration
- **Learning Rate**: 1e-5 (optimized for ModernBERT)
- **Batch Size**: 8 per device (can increase due to efficiency)
- **Sequence Length**: 4096 (can increase to 8192)
- **Epochs**: 50
- **Optimizer**: AdamW with weight decay 0.001
- **Warmup**: 10% of training steps

## Performance Benefits

### Compared to Llama-3-8B
- **20x smaller**: 395M vs 8B parameters
- **2-3x faster**: More efficient inference
- **Better context**: Bidirectional attention for reward modeling
- **Longer sequences**: Native 8192 token support

### Compared to Longformer
- **More efficient**: Modern architecture with Flash Attention
- **Better performance**: State-of-the-art encoder design
- **Easier deployment**: Smaller model size

## Integration with Existing Pipeline

The ModernBERT pipeline integrates seamlessly with the existing FG-PRM workflow:

1. **Same directory structure**: Files follow existing naming conventions
2. **Compatible data**: Uses existing ORM training data
3. **Similar interface**: Same command-line arguments and options
4. **Consistent outputs**: Compatible model outputs and metrics

## Troubleshooting

### Common Issues

1. **CUDA Out of Memory**
   - Reduce `per_device_train_batch_size` from 8 to 4 or 2
   - Reduce `max_seq_length` from 4096 to 2048

2. **Model Loading Issues**
   - Ensure internet connection for downloading ModernBERT-large
   - Check HuggingFace token if using private models

3. **Data Format Errors**
   - Verify JSON format with `jq` or similar tool
   - Check that text and feedback_tags have same length

### Performance Tuning

1. **Increase Batch Size**: If you have more GPU memory
2. **Adjust Learning Rate**: Try 5e-6 or 2e-5 for different convergence
3. **Longer Sequences**: Increase to 8192 for longer documents
4. **More Epochs**: Increase for better convergence on small datasets

## Example Training Configurations

### Available Error Types
The training script includes configurations for all error types:
- Calculation-Error
- Context-Inconsistency  
- Fabrication
- Factual-Inconsistency
- Instruction-Inconsistency
- Logical-Inconsistency

### Uncomment Sections
To train on different error types, uncomment the relevant sections in `rpe_train_fact_rm_modernbert.sh`.

## Monitoring

The pipeline supports Weights & Biases (wandb) logging:
- Training loss and learning rate
- Validation accuracy, precision, recall, F1
- Confusion matrix components
- Model parameters and gradients

## Next Steps

After training, you can:
1. **Evaluate**: Use the binary evaluation script
2. **Deploy**: Load the model for inference
3. **Fine-tune**: Continue training on domain-specific data
4. **Ensemble**: Combine with other reward models
