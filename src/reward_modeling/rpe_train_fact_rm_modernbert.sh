#!/bin/bash
set -e

gpu_num=$(echo $CUDA_VISIBLE_DEVICES | awk -F ',' '{print NF}')

## train reward model for ORM (Binary Classification) - Comprehensive Multi-Error Dataset
## This training uses PROPER 80/10/10 sixinone dataset splits with NO DATA LEAKAGE:
## - Train: 460 examples (78.3% with errors) - 80% of total data
## - Validation: 57 examples (77.2% with errors) - 10% for model selection
## - Test: 59 examples (81.4% with errors) - 10% for final evaluation
## Contains all 6 error types: Calculation-Error, Context-Inconsistency, Fabrication,
## Factual-Inconsistency, Instruction-Inconsistency, Logical-Inconsistency
## Binary classification: 0 = no error (O), 1 = has any error type
## Stratified sampling ensures balanced error type representation across all splits
torchrun --nproc_per_node $gpu_num --standalone --nnodes=1 ./reward_modeling/run_fg_rm_modernbert.py \
                --model_name_or_path answerdotai/ModernBERT-large \
                --train_file ../data/hallucination_sample/synthetic_sixinone_train_80.json \
                --validation_file ../data/hallucination_sample/synthetic_sixinone_val_10.json \
                --test_file ../data/hallucination_sample/synthetic_sixinone_test_10.json \
                --output_dir ../models/orm_modernbert_large_sixinone_80_10_10 \
                --do_train \
                --do_eval \
                --do_predict \
                --bf16 \
                --num_train_epochs 50 \
                --per_device_train_batch_size 8 \
                --per_device_eval_batch_size 8 \
                --evaluation_strategy epoch \
                --logging_strategy epoch \
                --save_strategy epoch \
                --load_best_model_at_end \
                --metric_for_best_model f1 \
                --max_seq_length 4096 \
                --report_to wandb \
                --save_total_limit 3 \
                --learning_rate 0.00002 \
                --weight_decay 0.001 \
                --warmup_ratio 0.1 \
                --gradient_accumulation_steps 1

## Alternative: train reward model for individual error types (Binary Classification)
## Uncomment any of the sections below to train on specific error types

## train reward model for ORM (Binary Classification) - Calculation-Error only
#torchrun --nproc_per_node $gpu_num --standalone --nnodes=1 ./reward_modeling/run_fg_rm_modernbert.py \
#                --model_name_or_path answerdotai/ModernBERT-large \
#                --train_file ../data/hallucination_sample/synthetic_Calculation-Error_orm_train.json \
#                --validation_file ../data/hallucination_sample/synthetic_Calculation-Error_orm_dev.json \
#                --test_file ../data/hallucination_sample/synthetic_Calculation-Error_orm_dev.json \
#                --output_dir ../models/orm_modernbert_large_Calculation-Error \
#                --do_train \
#                --do_eval \
#                --do_predict \
#                --bf16 \
#                --num_train_epochs 50 \
#                --per_device_train_batch_size 8 \
#                --per_device_eval_batch_size 8 \
#                --evaluation_strategy epoch \
#                --logging_strategy epoch \
#                --save_strategy epoch \
#                --load_best_model_at_end \
#                --metric_for_best_model f1 \
#                --max_seq_length 4096 \
#                --report_to wandb \
#                --save_total_limit 2 \
#                --learning_rate 0.00001 \
#                --weight_decay 0.001 \
#                --warmup_ratio 0.1

## train reward model for ORM (Binary Classification) - Context-Inconsistency only
#torchrun --nproc_per_node $gpu_num --standalone --nnodes=1 ./reward_modeling/run_fg_rm_modernbert.py \
#                --model_name_or_path answerdotai/ModernBERT-large \
#                --train_file ../data/hallucination_sample/synthetic_Context-Inconsistency_orm_train.json \
#                --validation_file ../data/hallucination_sample/synthetic_Context-Inconsistency_orm_dev.json \
#                --test_file ../data/hallucination_sample/synthetic_Context-Inconsistency_orm_dev.json \
#                --output_dir ../models/orm_modernbert_large_Context-Inconsistency \
#                --do_train \
#                --do_eval \
#                --do_predict \
#                --bf16 \
#                --num_train_epochs 50 \
#                --per_device_train_batch_size 8 \
#                --per_device_eval_batch_size 8 \
#                --evaluation_strategy epoch \
#                --logging_strategy epoch \
#                --save_strategy epoch \
#                --load_best_model_at_end \
#                --metric_for_best_model accuracy \
#                --max_seq_length 4096 \
#                --report_to wandb \
#                --save_total_limit 2 \
#                --learning_rate 0.00001 \
#                --weight_decay 0.001 \
#                --warmup_ratio 0.1

## train reward model for ORM (Binary Classification) - Fabrication
#torchrun --nproc_per_node $gpu_num --standalone --nnodes=1 ./reward_modeling/run_fg_rm_modernbert.py \
#                --model_name_or_path answerdotai/ModernBERT-large \
#                --train_file ../data/hallucination_sample/synthetic_Fabrication_orm_train.json \
#                --validation_file ../data/hallucination_sample/synthetic_Fabrication_orm_dev.json \
#                --test_file ../data/hallucination_sample/synthetic_Fabrication_orm_dev.json \
#                --output_dir ../models/orm_modernbert_large_Fabrication \
#                --do_train \
#                --do_eval \
#                --do_predict \
#                --bf16 \
#                --num_train_epochs 50 \
#                --per_device_train_batch_size 8 \
#                --per_device_eval_batch_size 8 \
#                --evaluation_strategy epoch \
#                --logging_strategy epoch \
#                --save_strategy epoch \
#                --load_best_model_at_end \
#                --metric_for_best_model accuracy \
#                --max_seq_length 4096 \
#                --report_to wandb \
#                --save_total_limit 2 \
#                --learning_rate 0.00001 \
#                --weight_decay 0.001 \
#                --warmup_ratio 0.1

## train reward model for ORM (Binary Classification) - Factual-Inconsistency
#torchrun --nproc_per_node $gpu_num --standalone --nnodes=1 ./reward_modeling/run_fg_rm_modernbert.py \
#                --model_name_or_path answerdotai/ModernBERT-large \
#                --train_file ../data/hallucination_sample/synthetic_Factual-Inconsistency_orm_train.json \
#                --validation_file ../data/hallucination_sample/synthetic_Factual-Inconsistency_orm_dev.json \
#                --test_file ../data/hallucination_sample/synthetic_Factual-Inconsistency_orm_dev.json \
#                --output_dir ../models/orm_modernbert_large_Factual-Inconsistency \
#                --do_train \
#                --do_eval \
#                --do_predict \
#                --bf16 \
#                --num_train_epochs 50 \
#                --per_device_train_batch_size 8 \
#                --per_device_eval_batch_size 8 \
#                --evaluation_strategy epoch \
#                --logging_strategy epoch \
#                --save_strategy epoch \
#                --load_best_model_at_end \
#                --metric_for_best_model accuracy \
#                --max_seq_length 4096 \
#                --report_to wandb \
#                --save_total_limit 2 \
#                --learning_rate 0.00001 \
#                --weight_decay 0.001 \
#                --warmup_ratio 0.1

## train reward model for ORM (Binary Classification) - Instruction-Inconsistency
#torchrun --nproc_per_node $gpu_num --standalone --nnodes=1 ./reward_modeling/run_fg_rm_modernbert.py \
#                --model_name_or_path answerdotai/ModernBERT-large \
#                --train_file ../data/hallucination_sample/synthetic_Instruction-Inconsistency_orm_train.json \
#                --validation_file ../data/hallucination_sample/synthetic_Instruction-Inconsistency_orm_dev.json \
#                --test_file ../data/hallucination_sample/synthetic_Instruction-Inconsistency_orm_dev.json \
#                --output_dir ../models/orm_modernbert_large_Instruction-Inconsistency \
#                --do_train \
#                --do_eval \
#                --do_predict \
#                --bf16 \
#                --num_train_epochs 50 \
#                --per_device_train_batch_size 8 \
#                --per_device_eval_batch_size 8 \
#                --evaluation_strategy epoch \
#                --logging_strategy epoch \
#                --save_strategy epoch \
#                --load_best_model_at_end \
#                --metric_for_best_model accuracy \
#                --max_seq_length 4096 \
#                --report_to wandb \
#                --save_total_limit 2 \
#                --learning_rate 0.00001 \
#                --weight_decay 0.001 \
#                --warmup_ratio 0.1

## train reward model for ORM (Binary Classification) - Logical-Inconsistency
#torchrun --nproc_per_node $gpu_num --standalone --nnodes=1 ./reward_modeling/run_fg_rm_modernbert.py \
#                --model_name_or_path answerdotai/ModernBERT-large \
#                --train_file ../data/hallucination_sample/synthetic_Logical-Inconsistency_orm_train.json \
#                --validation_file ../data/hallucination_sample/synthetic_Logical-Inconsistency_orm_dev.json \
#                --test_file ../data/hallucination_sample/synthetic_Logical-Inconsistency_orm_dev.json \
#                --output_dir ../models/orm_modernbert_large_Logical-Inconsistency \
#                --do_train \
#                --do_eval \
#                --do_predict \
#                --bf16 \
#                --num_train_epochs 50 \
#                --per_device_train_batch_size 8 \
#                --per_device_eval_batch_size 8 \
#                --evaluation_strategy epoch \
#                --logging_strategy epoch \
#                --save_strategy epoch \
#                --load_best_model_at_end \
#                --metric_for_best_model accuracy \
#                --max_seq_length 4096 \
#                --report_to wandb \
#                --save_total_limit 2 \
#                --learning_rate 0.00001 \
#                --weight_decay 0.001 \
#                --warmup_ratio 0.1
