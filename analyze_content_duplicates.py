#!/usr/bin/env python3
"""
<PERSON>ript to analyze content-based duplicates in concatenated JSON files.
Considers entries as duplicates if both "text" and "feedback_tags" fields match exactly.
"""

import json
import os
import hashlib
from collections import defaultdict, Counter

def create_content_hash(entry):
    """
    Create a hash of the content fields (text + feedback_tags) for duplicate detection.
    
    Args:
        entry (dict): JSON entry with 'text' and 'feedback_tags' fields
    
    Returns:
        str: Hash string representing the content
    """
    # Convert text and feedback_tags to strings for consistent hashing
    text_str = str(entry.get('text', []))
    feedback_str = str(entry.get('feedback_tags', []))
    
    # Combine both fields
    combined_content = text_str + "|" + feedback_str
    
    # Create hash
    return hashlib.md5(combined_content.encode('utf-8')).hexdigest()

def analyze_content_duplicates(file_path):
    """
    Analyze content-based duplicates in a JSON lines file.
    
    Args:
        file_path (str): Path to the JSON lines file
    
    Returns:
        dict: Analysis results
    """
    if not os.path.exists(file_path):
        return {"error": f"File not found: {file_path}"}
    
    print(f"Analyzing content duplicates in: {os.path.basename(file_path)}")
    print(f"File size: {os.path.getsize(file_path) / (1024*1024):.1f} MB")
    
    # Storage for analysis
    content_hashes = []
    entries_by_hash = defaultdict(list)
    total_entries = 0
    parse_errors = 0
    
    # Read all entries and create content hashes
    with open(file_path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if not line:
                continue
                
            try:
                entry = json.loads(line)
                total_entries += 1
                
                # Create content hash
                content_hash = create_content_hash(entry)
                content_hashes.append(content_hash)
                
                # Store entry with its hash for duplicate analysis
                entries_by_hash[content_hash].append({
                    'line_num': line_num,
                    'id': entry.get('id', 'NO_ID'),
                    'text_length': len(entry.get('text', [])),
                    'feedback_tags_count': len(entry.get('feedback_tags', [])),
                    'entry': entry  # Store full entry for examples
                })
                
            except json.JSONDecodeError as e:
                parse_errors += 1
                print(f"Error parsing JSON at line {line_num}: {e}")
                continue
    
    # Analyze duplicates
    hash_counts = Counter(content_hashes)
    duplicate_hashes = {h: count for h, count in hash_counts.items() if count > 1}
    
    unique_entries = len(hash_counts)
    total_duplicate_entries = sum(count - 1 for count in duplicate_hashes.values())
    
    # Collect duplicate examples
    duplicate_examples = []
    for content_hash, count in list(duplicate_hashes.items())[:5]:  # Get first 5 examples
        entries_with_hash = entries_by_hash[content_hash]
        example = {
            'content_hash': content_hash,
            'occurrence_count': count,
            'entries': entries_with_hash[:3],  # Show first 3 occurrences
            'sample_text_preview': entries_with_hash[0]['entry']['text'][:10] if entries_with_hash[0]['entry'].get('text') else [],
            'sample_feedback_tags': entries_with_hash[0]['entry'].get('feedback_tags', [])[:5]
        }
        duplicate_examples.append(example)
    
    # Analysis results
    results = {
        "file_path": file_path,
        "file_name": os.path.basename(file_path),
        "total_entries": total_entries,
        "parse_errors": parse_errors,
        "unique_content_entries": unique_entries,
        "duplicate_content_groups": len(duplicate_hashes),
        "total_duplicate_entries": total_duplicate_entries,
        "duplication_rate": (total_duplicate_entries / total_entries * 100) if total_entries > 0 else 0,
        "duplicate_examples": duplicate_examples,
        "duplicate_distribution": dict(Counter(duplicate_hashes.values()))
    }
    
    return results

def print_analysis_results(results):
    """Print formatted analysis results."""
    
    if "error" in results:
        print(f"Error: {results['error']}")
        return
    
    print(f"\n=== Content Duplicate Analysis: {results['file_name']} ===")
    print(f"Total entries: {results['total_entries']:,}")
    print(f"Parse errors: {results['parse_errors']}")
    print(f"Unique content entries: {results['unique_content_entries']:,}")
    print(f"Duplicate content groups: {results['duplicate_content_groups']:,}")
    print(f"Total duplicate entries: {results['total_duplicate_entries']:,}")
    print(f"Duplication rate: {results['duplication_rate']:.2f}%")
    
    if results['total_duplicate_entries'] > 0:
        print(f"\n--- Duplicate Distribution ---")
        for dup_count, num_groups in sorted(results['duplicate_distribution'].items()):
            total_extras = (dup_count - 1) * num_groups
            print(f"  {num_groups} content groups appear {dup_count} times each ({total_extras} extra entries)")
        
        print(f"\n--- Sample Duplicate Examples ---")
        for i, example in enumerate(results['duplicate_examples'], 1):
            print(f"\nExample {i}:")
            print(f"  Content appears {example['occurrence_count']} times")
            print(f"  Text preview: {example['sample_text_preview']}")
            print(f"  Feedback tags preview: {example['sample_feedback_tags']}")
            print(f"  Found in entries with IDs:")
            for entry_info in example['entries']:
                print(f"    - Line {entry_info['line_num']}: ID '{entry_info['id']}' (text: {entry_info['text_length']} tokens, tags: {entry_info['feedback_tags_count']})")
    else:
        print(f"\n✅ No content-based duplicates found!")

def main():
    """Main function to analyze both training and development files."""
    
    base_dir = "data/hallucination_sample"
    
    files_to_analyze = [
        {
            'path': os.path.join(base_dir, "synthetic_AllErr_train.json"),
            'name': 'Training Dataset'
        },
        {
            'path': os.path.join(base_dir, "synthetic_AllErr_dev.json"),
            'name': 'Development Dataset'
        }
    ]
    
    print("=== Content-Based Duplicate Analysis ===")
    print("Analyzing duplicates based on identical 'text' AND 'feedback_tags' fields")
    print("(Ignoring 'id' field differences)\n")
    
    all_results = {}
    
    for file_info in files_to_analyze:
        file_path = file_info['path']
        dataset_name = file_info['name']
        
        if not os.path.exists(file_path):
            print(f"❌ {dataset_name}: File not found - {file_path}")
            continue
        
        # Analyze the file
        results = analyze_content_duplicates(file_path)
        all_results[dataset_name] = results
        
        # Print results
        print_analysis_results(results)
        print("\n" + "="*80)
    
    # Overall summary
    print(f"\n=== Overall Summary ===")
    total_entries_all = 0
    total_unique_all = 0
    total_duplicates_all = 0
    
    for dataset_name, results in all_results.items():
        if "error" not in results:
            total_entries_all += results['total_entries']
            total_unique_all += results['unique_content_entries']
            total_duplicates_all += results['total_duplicate_entries']
            
            print(f"{dataset_name}:")
            print(f"  Entries: {results['total_entries']:,} | Unique: {results['unique_content_entries']:,} | Duplicates: {results['total_duplicate_entries']:,} ({results['duplication_rate']:.2f}%)")
    
    if total_entries_all > 0:
        overall_dup_rate = (total_duplicates_all / total_entries_all) * 100
        print(f"\nCombined Statistics:")
        print(f"  Total entries: {total_entries_all:,}")
        print(f"  Total unique content: {total_unique_all:,}")
        print(f"  Total duplicates: {total_duplicates_all:,}")
        print(f"  Overall duplication rate: {overall_dup_rate:.2f}%")
    
    print(f"\n✅ Content duplicate analysis completed!")

if __name__ == "__main__":
    main()
