#!/usr/bin/env python3
"""
Integration test script for ModernBERT Fine-Grained Process Reward Models.

This script validates that the trained models integrate properly with the existing
verification pipeline and produce expected results.
"""

import os
import sys
import json
import logging
import tempfile
from typing import Dict, List, Any
import torch
import numpy as np

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from transformers import AutoTokenizer
from fine_tuning.my_modernbert import ModernBertForTokenClassification
from fine_tuning.reward_modernbert import ModernBertFineGrainedReward
from verification.verify_modernbert import ModernBertVerifier

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModernBertIntegrationTester:
    """Comprehensive integration tester for ModernBERT PRM system."""
    
    def __init__(self, model_base_dir: str):
        self.model_base_dir = model_base_dir
        self.error_types = [
            "Calculation-Error",
            "Fabrication", 
            "Context-Inconsistency",
            "Factual-Inconsistency",
            "Instruction-Inconsistency",
            "Logical-Inconsistency"
        ]
        self.test_results = {}
    
    def test_model_loading(self) -> bool:
        """Test that all 6 models can be loaded successfully."""
        logger.info("Testing model loading...")
        
        loaded_models = 0
        for error_type in self.error_types:
            model_dir = os.path.join(self.model_base_dir, error_type.replace("-", "_").lower())
            
            try:
                if os.path.exists(model_dir):
                    # Test tokenizer loading
                    tokenizer = AutoTokenizer.from_pretrained(model_dir)
                    
                    # Test model loading
                    model = ModernBertForTokenClassification.from_pretrained(model_dir)
                    
                    # Test model can be put in eval mode
                    model.eval()
                    
                    logger.info(f"✓ Successfully loaded {error_type} model")
                    loaded_models += 1
                else:
                    logger.warning(f"✗ Model directory not found: {model_dir}")
            except Exception as e:
                logger.error(f"✗ Failed to load {error_type} model: {str(e)}")
        
        success = loaded_models > 0
        self.test_results['model_loading'] = {
            'success': success,
            'loaded_models': loaded_models,
            'total_models': len(self.error_types)
        }
        
        return success
    
    def test_reward_system_integration(self) -> bool:
        """Test the reward system integration."""
        logger.info("Testing reward system integration...")
        
        try:
            # Create a dummy tokenizer for testing
            tokenizer = AutoTokenizer.from_pretrained("answerdotai/ModernBERT-base")
            
            # Test reward system initialization
            reward_system = ModernBertFineGrainedReward(
                tokenizer=tokenizer,
                calculation_error_model_ckpt=os.path.join(self.model_base_dir, "calculation_error"),
                fabrication_model_ckpt=os.path.join(self.model_base_dir, "fabrication"),
                context_inconsistency_model_ckpt=os.path.join(self.model_base_dir, "context_inconsistency"),
                factual_inconsistency_model_ckpt=os.path.join(self.model_base_dir, "factual_inconsistency"),
                instruction_inconsistency_model_ckpt=os.path.join(self.model_base_dir, "instruction_inconsistency"),
                logical_inconsistency_model_ckpt=os.path.join(self.model_base_dir, "logical_inconsistency"),
                kl_coef=0.1
            )
            
            # Test with dummy data
            dummy_text = "What is 2 + 2? The answer is 4."
            dummy_metadata = [{"prompt": "What is 2 + 2?"}]
            
            # Create dummy tensors
            dummy_input_ids = torch.tensor([[1, 2, 3, 4, 5]])
            dummy_attention_mask = torch.tensor([[1, 1, 1, 1, 1]])
            
            # Test reward computation
            rewards = reward_system.get_reward(
                prompts_input_ids=dummy_input_ids,
                prompts_attention_mask=dummy_attention_mask,
                generated_input_ids=dummy_input_ids,
                generated_attention_mask=dummy_attention_mask,
                generated_texts=[dummy_text],
                metadata=dummy_metadata
            )
            
            # Validate reward structure
            assert 'rewards/raw' in rewards
            assert isinstance(rewards['rewards/raw'], list)
            
            logger.info("✓ Reward system integration successful")
            self.test_results['reward_system'] = {'success': True}
            return True
            
        except Exception as e:
            logger.error(f"✗ Reward system integration failed: {str(e)}")
            self.test_results['reward_system'] = {'success': False, 'error': str(e)}
            return False
    
    def test_verification_system(self) -> bool:
        """Test the verification system."""
        logger.info("Testing verification system...")
        
        try:
            # Initialize verifier
            verifier = ModernBertVerifier(self.model_base_dir, device="cpu")
            
            # Test with dummy data
            question = "What is 2 + 2?"
            candidates = [
                "2 + 2 = 4. This is correct.",
                "2 + 2 = 5. This is wrong."
            ]
            
            # Test verification
            best_idx, results = verifier.verify_candidates(question, candidates)
            
            # Validate results
            assert isinstance(best_idx, int)
            assert 0 <= best_idx < len(candidates)
            assert len(results) == len(candidates)
            
            for result in results:
                assert 'overall_score' in result
                assert 'step_scores' in result
                assert 'error_type_scores' in result
            
            logger.info(f"✓ Verification system successful (selected candidate {best_idx})")
            self.test_results['verification_system'] = {
                'success': True,
                'selected_candidate': best_idx,
                'num_candidates': len(candidates)
            }
            return True
            
        except Exception as e:
            logger.error(f"✗ Verification system failed: {str(e)}")
            self.test_results['verification_system'] = {'success': False, 'error': str(e)}
            return False
    
    def test_data_format_compatibility(self) -> bool:
        """Test compatibility with expected data formats."""
        logger.info("Testing data format compatibility...")
        
        try:
            # Test input format: question + y1 [SEP] y2 [SEP] ...
            test_input = "What is 2 + 2? Step 1: Add 2 and 2. [SEP] Step 2: The result is 4. [SEP]"
            
            # Test tokenization
            tokenizer = AutoTokenizer.from_pretrained("answerdotai/ModernBERT-base")
            if "[SEP]" not in tokenizer.vocab:
                tokenizer.add_tokens(["[SEP]"])
            
            tokens = tokenizer.tokenize(test_input)
            input_ids = tokenizer.encode(test_input)
            
            # Validate [SEP] tokens are present
            sep_token_id = tokenizer.convert_tokens_to_ids("[SEP]")
            sep_positions = [i for i, token_id in enumerate(input_ids) if token_id == sep_token_id]
            
            assert len(sep_positions) > 0, "No [SEP] tokens found in input"
            
            logger.info(f"✓ Data format compatibility successful ({len(sep_positions)} [SEP] tokens found)")
            self.test_results['data_format'] = {
                'success': True,
                'sep_tokens_found': len(sep_positions),
                'input_length': len(input_ids)
            }
            return True
            
        except Exception as e:
            logger.error(f"✗ Data format compatibility failed: {str(e)}")
            self.test_results['data_format'] = {'success': False, 'error': str(e)}
            return False
    
    def test_score_aggregation(self) -> bool:
        """Test that scores are properly aggregated across the 6 PRMs."""
        logger.info("Testing score aggregation...")
        
        try:
            # This test validates the core requirement: "Sum the 6 PRM scores across steps during inference"
            
            # Create dummy scores for 6 error types
            dummy_scores = {
                'calculation_error': [0.8, 0.9, 0.7],
                'fabrication': [0.9, 0.8, 0.8],
                'context_inconsistency': [0.7, 0.9, 0.9],
                'factual_inconsistency': [0.8, 0.7, 0.8],
                'instruction_inconsistency': [0.9, 0.8, 0.7],
                'logical_inconsistency': [0.8, 0.8, 0.8]
            }
            
            # Test aggregation logic (sum across error types)
            num_steps = len(dummy_scores['calculation_error'])
            aggregated_scores = []
            
            for step in range(num_steps):
                step_score = sum(scores[step] for scores in dummy_scores.values())
                aggregated_scores.append(step_score)
            
            # Validate aggregation
            expected_scores = [5.0, 4.9, 4.7]  # Sum of each step across all error types
            
            for i, (actual, expected) in enumerate(zip(aggregated_scores, expected_scores)):
                assert abs(actual - expected) < 0.001, f"Step {i}: expected {expected}, got {actual}"
            
            logger.info("✓ Score aggregation successful")
            self.test_results['score_aggregation'] = {
                'success': True,
                'aggregated_scores': aggregated_scores,
                'num_steps': num_steps
            }
            return True
            
        except Exception as e:
            logger.error(f"✗ Score aggregation failed: {str(e)}")
            self.test_results['score_aggregation'] = {'success': False, 'error': str(e)}
            return False
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all integration tests."""
        logger.info("Starting comprehensive integration tests...")
        
        tests = [
            ('Model Loading', self.test_model_loading),
            ('Data Format Compatibility', self.test_data_format_compatibility),
            ('Score Aggregation', self.test_score_aggregation),
            ('Reward System Integration', self.test_reward_system_integration),
            ('Verification System', self.test_verification_system),
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n--- Running {test_name} Test ---")
            try:
                if test_func():
                    passed_tests += 1
                    logger.info(f"✓ {test_name} PASSED")
                else:
                    logger.error(f"✗ {test_name} FAILED")
            except Exception as e:
                logger.error(f"✗ {test_name} FAILED with exception: {str(e)}")
        
        # Summary
        success_rate = passed_tests / total_tests
        overall_success = success_rate >= 0.8  # 80% pass rate required
        
        summary = {
            'overall_success': overall_success,
            'passed_tests': passed_tests,
            'total_tests': total_tests,
            'success_rate': success_rate,
            'test_results': self.test_results
        }
        
        logger.info(f"\n{'='*50}")
        logger.info("INTEGRATION TEST SUMMARY")
        logger.info(f"{'='*50}")
        logger.info(f"Tests Passed: {passed_tests}/{total_tests}")
        logger.info(f"Success Rate: {success_rate:.1%}")
        logger.info(f"Overall Result: {'✓ PASS' if overall_success else '✗ FAIL'}")
        logger.info(f"{'='*50}")
        
        return summary


def main():
    """Main function for running integration tests."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test ModernBERT PRM integration")
    parser.add_argument("--model_base_dir", type=str, default="../models/prm_modernbert",
                        help="Base directory containing trained PRM models")
    parser.add_argument("--output_file", type=str, default="integration_test_results.json",
                        help="Output file for test results")
    
    args = parser.parse_args()
    
    # Run tests
    tester = ModernBertIntegrationTester(args.model_base_dir)
    results = tester.run_all_tests()
    
    # Save results
    with open(args.output_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"Test results saved to: {args.output_file}")
    
    # Exit with appropriate code
    sys.exit(0 if results['overall_success'] else 1)


if __name__ == "__main__":
    main()
