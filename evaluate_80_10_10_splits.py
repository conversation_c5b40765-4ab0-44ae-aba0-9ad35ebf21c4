#!/usr/bin/env python3
"""
Comprehensive evaluation script for ModernBERT with proper 80/10/10 splits.
Ensures proper evaluation without data leakage using independent test set.
"""

import json
import os
import sys
from pathlib import Path
from collections import Counter
import numpy as np


def load_predictions(predictions_file):
    """Load predictions from the model output file."""
    predictions = []
    
    if not os.path.exists(predictions_file):
        print(f"❌ Predictions file not found: {predictions_file}")
        return None
    
    with open(predictions_file, 'r') as f:
        for line in f:
            parts = line.strip().split('\t')
            if len(parts) >= 2:
                try:
                    pred = int(parts[1])
                    predictions.append(pred)
                except ValueError:
                    continue
    
    return predictions


def load_test_labels(test_file):
    """Load true labels from the test dataset."""
    ERROR_TYPES = {
        'Calculation-Error', 'Context-Inconsistency', 'Fabrication',
        'Factual-Inconsistency', 'Instruction-Inconsistency', 
        'Logical-Inconsistency', 'F-ERR'
    }
    
    labels = []
    examples = []
    
    if not os.path.exists(test_file):
        print(f"❌ Test file not found: {test_file}")
        return None, None
    
    with open(test_file, 'r') as f:
        for line in f:
            try:
                data = json.loads(line.strip())
                feedback_tags = data.get('feedback_tags', [])
                
                # Convert to binary label
                has_error = any(tag in ERROR_TYPES for tag in feedback_tags)
                binary_label = 1 if has_error else 0
                
                labels.append(binary_label)
                examples.append(data)
                
            except json.JSONDecodeError:
                continue
    
    return labels, examples


def calculate_comprehensive_metrics(predictions, labels):
    """Calculate comprehensive binary classification metrics."""
    if len(predictions) != len(labels):
        print(f"❌ Mismatch: {len(predictions)} predictions vs {len(labels)} labels")
        return None
    
    predictions = np.array(predictions)
    labels = np.array(labels)
    
    # Confusion matrix components
    tp = ((predictions == 1) & (labels == 1)).sum()
    fp = ((predictions == 1) & (labels == 0)).sum()
    fn = ((predictions == 0) & (labels == 1)).sum()
    tn = ((predictions == 0) & (labels == 0)).sum()
    
    # Basic metrics
    accuracy = (tp + tn) / (tp + fp + fn + tn)
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
    specificity = tn / (tn + fp) if (tn + fp) > 0 else 0.0
    balanced_accuracy = (recall + specificity) / 2
    
    # Additional metrics
    npv = tn / (tn + fn) if (tn + fn) > 0 else 0.0  # Negative Predictive Value
    fpr = fp / (fp + tn) if (fp + tn) > 0 else 0.0  # False Positive Rate
    fnr = fn / (fn + tp) if (fn + tp) > 0 else 0.0  # False Negative Rate
    
    return {
        'accuracy': float(accuracy),
        'balanced_accuracy': float(balanced_accuracy),
        'precision': float(precision),
        'recall': float(recall),
        'f1': float(f1),
        'specificity': float(specificity),
        'npv': float(npv),
        'fpr': float(fpr),
        'fnr': float(fnr),
        'tp': int(tp),
        'fp': int(fp),
        'fn': int(fn),
        'tn': int(tn),
        'total_samples': len(labels),
        'positive_samples': int((labels == 1).sum()),
        'negative_samples': int((labels == 0).sum())
    }


def analyze_error_types_performance(examples, predictions, labels):
    """Analyze performance by individual error type."""
    ERROR_TYPES = {
        'Calculation-Error', 'Context-Inconsistency', 'Fabrication',
        'Factual-Inconsistency', 'Instruction-Inconsistency', 
        'Logical-Inconsistency', 'F-ERR'
    }
    
    error_type_performance = {}
    
    for error_type in ERROR_TYPES:
        type_predictions = []
        type_labels = []
        type_indices = []
        
        for i, example in enumerate(examples):
            feedback_tags = example.get('feedback_tags', [])
            has_this_error = any(tag == error_type for tag in feedback_tags)
            
            if has_this_error:
                type_predictions.append(predictions[i])
                type_labels.append(labels[i])
                type_indices.append(i)
        
        if type_predictions:
            type_metrics = calculate_comprehensive_metrics(type_predictions, type_labels)
            if type_metrics:
                error_type_performance[error_type] = {
                    'count': len(type_predictions),
                    'accuracy': type_metrics['accuracy'],
                    'precision': type_metrics['precision'],
                    'recall': type_metrics['recall'],
                    'f1': type_metrics['f1'],
                    'examples_indices': type_indices
                }
    
    return error_type_performance


def print_comprehensive_results(metrics, error_type_performance):
    """Print comprehensive evaluation results."""
    print("🎯 INDEPENDENT TEST SET EVALUATION RESULTS (80/10/10 SPLIT)")
    print("=" * 70)
    
    print("📊 OVERALL PERFORMANCE:")
    print(f"  Accuracy: {metrics['accuracy']:.4f}")
    print(f"  Balanced Accuracy: {metrics['balanced_accuracy']:.4f}")
    print(f"  Precision: {metrics['precision']:.4f}")
    print(f"  Recall (Sensitivity): {metrics['recall']:.4f}")
    print(f"  F1 Score: {metrics['f1']:.4f}")
    print(f"  Specificity: {metrics['specificity']:.4f}")
    print(f"  NPV (Negative Predictive Value): {metrics['npv']:.4f}")
    
    print(f"\n🔢 CONFUSION MATRIX:")
    print(f"                 Predicted")
    print(f"                 0    1")
    print(f"  Actual    0   {metrics['tn']:2d}   {metrics['fp']:2d}")
    print(f"            1   {metrics['fn']:2d}   {metrics['tp']:2d}")
    
    print(f"\n📈 DATASET STATISTICS:")
    print(f"  Total Test Samples: {metrics['total_samples']}")
    print(f"  Positive Samples: {metrics['positive_samples']} ({metrics['positive_samples']/metrics['total_samples']*100:.1f}%)")
    print(f"  Negative Samples: {metrics['negative_samples']} ({metrics['negative_samples']/metrics['total_samples']*100:.1f}%)")
    
    print(f"\n📉 ERROR RATES:")
    print(f"  False Positive Rate: {metrics['fpr']:.4f}")
    print(f"  False Negative Rate: {metrics['fnr']:.4f}")
    
    if error_type_performance:
        print(f"\n🎯 PERFORMANCE BY ERROR TYPE:")
        for error_type, perf in sorted(error_type_performance.items()):
            print(f"  {error_type}:")
            print(f"    Examples: {perf['count']}")
            print(f"    Accuracy: {perf['accuracy']:.4f}")
            print(f"    Precision: {perf['precision']:.4f}")
            print(f"    Recall: {perf['recall']:.4f}")
            print(f"    F1: {perf['f1']:.4f}")
    
    # Performance assessment
    print(f"\n✅ EVALUATION ASSESSMENT:")
    if metrics['f1'] >= 0.85:
        print("  🟢 Excellent performance (F1 ≥ 0.85)")
    elif metrics['f1'] >= 0.75:
        print("  🟡 Good performance (F1 ≥ 0.75)")
    elif metrics['f1'] >= 0.65:
        print("  🟠 Fair performance (F1 ≥ 0.65)")
    else:
        print("  🔴 Poor performance (F1 < 0.65)")
    
    if abs(metrics['precision'] - metrics['recall']) < 0.1:
        print("  ✅ Balanced precision and recall")
    else:
        print("  ⚠ Imbalanced precision and recall")
    
    if metrics['balanced_accuracy'] >= 0.8:
        print("  ✅ Good balanced accuracy (handles both classes well)")
    else:
        print("  ⚠ Consider class balancing techniques")


def main():
    """Main evaluation function."""
    if len(sys.argv) != 3:
        print("Usage: python evaluate_80_10_10_splits.py <predictions_file> <test_file>")
        print("Example:")
        print("  python evaluate_80_10_10_splits.py \\")
        print("    ../models/orm_modernbert_large_sixinone_80_10_10/predictions.txt \\")
        print("    ../data/hallucination_sample/synthetic_sixinone_test_10.json")
        sys.exit(1)
    
    predictions_file = sys.argv[1]
    test_file = sys.argv[2]
    
    print("🔍 LOADING DATA FOR 80/10/10 SPLIT EVALUATION")
    print("=" * 70)
    
    # Load predictions
    print(f"Loading predictions from: {predictions_file}")
    predictions = load_predictions(predictions_file)
    if predictions is None:
        sys.exit(1)
    print(f"  ✅ Loaded {len(predictions)} predictions")
    
    # Load test labels
    print(f"Loading test labels from: {test_file}")
    labels, examples = load_test_labels(test_file)
    if labels is None:
        sys.exit(1)
    print(f"  ✅ Loaded {len(labels)} test examples")
    
    # Verify data integrity
    if len(predictions) != len(labels):
        print(f"❌ Data mismatch: {len(predictions)} predictions vs {len(labels)} labels")
        sys.exit(1)
    
    print("  ✅ Data integrity verified")
    
    # Calculate comprehensive metrics
    print("\n🧮 CALCULATING COMPREHENSIVE METRICS...")
    metrics = calculate_comprehensive_metrics(predictions, labels)
    if metrics is None:
        sys.exit(1)
    
    # Analyze by error type
    print("🔍 ANALYZING BY ERROR TYPE...")
    error_type_performance = analyze_error_types_performance(examples, predictions, labels)
    
    # Print results
    print_comprehensive_results(metrics, error_type_performance)
    
    print(f"\n🔒 DATA LEAKAGE VERIFICATION:")
    print(f"  ✅ Using independent test set (10% of total data)")
    print(f"  ✅ No overlap with training (80%) or validation (10%) data")
    print(f"  ✅ Proper 80/10/10 split with stratified sampling")
    print(f"  ✅ Unbiased evaluation results")


if __name__ == "__main__":
    main()
