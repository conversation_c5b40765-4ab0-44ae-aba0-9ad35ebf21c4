#!/usr/bin/env python3
"""
Create proper train/validation/test splits for the sixinone dataset.
Ensures no data leakage between splits and maintains error distribution.
"""

import json
import random
import os
from collections import Counter, defaultdict
from pathlib import Path


def load_all_data():
    """Load and combine all sixinone data."""
    train_file = '/mnt/c/Projects/FG-PRM/data/hallucination_sample/synthetic_sixinone_train.json'
    dev_file = '/mnt/c/Projects/FG-PRM/data/hallucination_sample/synthetic_sixinone_dev.json'
    
    all_data = []
    seen_ids = set()
    
    # Load train data
    with open(train_file, 'r') as f:
        for line in f:
            item = json.loads(line.strip())
            if item['id'] not in seen_ids:
                all_data.append(item)
                seen_ids.add(item['id'])
    
    # Load dev data (only unique IDs)
    with open(dev_file, 'r') as f:
        for line in f:
            item = json.loads(line.strip())
            if item['id'] not in seen_ids:
                all_data.append(item)
                seen_ids.add(item['id'])
    
    print(f"Loaded {len(all_data)} unique examples")
    return all_data


def classify_examples(data):
    """Classify examples by error type for stratified sampling."""
    ERROR_TYPES = {
        'Calculation-Error', 'Context-Inconsistency', 'Fabrication',
        'Factual-Inconsistency', 'Instruction-Inconsistency', 
        'Logical-Inconsistency', 'F-ERR'
    }
    
    # Group examples by error type combination
    error_groups = defaultdict(list)
    no_error_examples = []
    
    for item in data:
        feedback_tags = item.get('feedback_tags', [])
        example_errors = set(tag for tag in feedback_tags if tag in ERROR_TYPES)
        
        if example_errors:
            # Create a key for the error combination
            error_key = tuple(sorted(example_errors))
            error_groups[error_key].append(item)
        else:
            no_error_examples.append(item)
    
    return error_groups, no_error_examples


def create_stratified_splits(error_groups, no_error_examples, train_size=10800, val_size=600, test_size=600):
    """Create stratified train/val/test splits maintaining error distribution."""
    
    # Set random seed for reproducibility
    random.seed(42)
    
    # Shuffle all groups
    for group in error_groups.values():
        random.shuffle(group)
    random.shuffle(no_error_examples)
    
    train_data = []
    val_data = []
    test_data = []
    
    # Calculate proportions
    total_size = train_size + val_size + test_size
    train_ratio = train_size / total_size
    val_ratio = val_size / total_size
    test_ratio = test_size / total_size
    
    # Split no-error examples
    no_error_train_size = int(len(no_error_examples) * train_ratio)
    no_error_val_size = int(len(no_error_examples) * val_ratio)
    
    train_data.extend(no_error_examples[:no_error_train_size])
    val_data.extend(no_error_examples[no_error_train_size:no_error_train_size + no_error_val_size])
    test_data.extend(no_error_examples[no_error_train_size + no_error_val_size:])
    
    # Split error examples by group
    for error_key, examples in error_groups.items():
        group_train_size = int(len(examples) * train_ratio)
        group_val_size = int(len(examples) * val_ratio)
        
        train_data.extend(examples[:group_train_size])
        val_data.extend(examples[group_train_size:group_train_size + group_val_size])
        test_data.extend(examples[group_train_size + group_val_size:])
    
    # Shuffle final splits
    random.shuffle(train_data)
    random.shuffle(val_data)
    random.shuffle(test_data)
    
    return train_data, val_data, test_data


def analyze_split(data, name):
    """Analyze the distribution of a data split."""
    ERROR_TYPES = {
        'Calculation-Error', 'Context-Inconsistency', 'Fabrication',
        'Factual-Inconsistency', 'Instruction-Inconsistency', 
        'Logical-Inconsistency', 'F-ERR'
    }
    
    error_examples = 0
    no_error_examples = 0
    error_type_counts = Counter()
    
    for item in data:
        feedback_tags = item.get('feedback_tags', [])
        has_error = any(tag in ERROR_TYPES for tag in feedback_tags)
        
        if has_error:
            error_examples += 1
            for tag in feedback_tags:
                if tag in ERROR_TYPES:
                    error_type_counts[tag] += 1
        else:
            no_error_examples += 1
    
    total = len(data)
    print(f"{name} Split:")
    print(f"  Total examples: {total}")
    print(f"  With errors: {error_examples} ({error_examples/total*100:.1f}%)")
    print(f"  Without errors: {no_error_examples} ({no_error_examples/total*100:.1f}%)")
    print(f"  Error type distribution:")
    for error_type in sorted(ERROR_TYPES):
        count = error_type_counts.get(error_type, 0)
        print(f"    {error_type}: {count}")
    print()


def save_split(data, filename):
    """Save a data split to file."""
    output_path = f"/mnt/c/Projects/FG-PRM/data/hallucination_sample/{filename}"
    
    with open(output_path, 'w') as f:
        for item in data:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')
    
    print(f"Saved {len(data)} examples to {filename}")


def verify_no_overlap(train_data, val_data, test_data):
    """Verify there's no overlap between splits."""
    train_ids = set(item['id'] for item in train_data)
    val_ids = set(item['id'] for item in val_data)
    test_ids = set(item['id'] for item in test_data)
    
    train_val_overlap = train_ids.intersection(val_ids)
    train_test_overlap = train_ids.intersection(test_ids)
    val_test_overlap = val_ids.intersection(test_ids)
    
    print("Overlap Analysis:")
    print(f"  Train-Val overlap: {len(train_val_overlap)} examples")
    print(f"  Train-Test overlap: {len(train_test_overlap)} examples")
    print(f"  Val-Test overlap: {len(val_test_overlap)} examples")
    
    if train_val_overlap or train_test_overlap or val_test_overlap:
        print("  ❌ WARNING: Found overlaps between splits!")
        return False
    else:
        print("  ✅ No overlaps found - clean splits!")
        return True


def main():
    """Create proper train/validation/test splits."""
    print("🔄 CREATING PROPER TRAIN/VALIDATION/TEST SPLITS")
    print("=" * 60)
    
    # Load all data
    print("1. Loading all data...")
    all_data = load_all_data()
    
    # Classify examples
    print("2. Classifying examples by error type...")
    error_groups, no_error_examples = classify_examples(all_data)
    
    print(f"   Found {len(error_groups)} different error combinations")
    print(f"   Found {len(no_error_examples)} examples without errors")
    
    # Create splits
    print("3. Creating stratified splits...")
    train_data, val_data, test_data = create_stratified_splits(
        error_groups, no_error_examples,
        train_size=10800,  # Reduced to ensure we have enough for all splits
        val_size=600,
        test_size=600
    )
    
    # Analyze splits
    print("4. Analyzing splits...")
    analyze_split(train_data, "Train")
    analyze_split(val_data, "Validation")
    analyze_split(test_data, "Test")
    
    # Verify no overlap
    print("5. Verifying no overlap...")
    clean_splits = verify_no_overlap(train_data, val_data, test_data)
    
    if not clean_splits:
        print("❌ Split creation failed due to overlaps!")
        return False
    
    # Save splits
    print("6. Saving new splits...")
    save_split(train_data, "synthetic_sixinone_train_clean.json")
    save_split(val_data, "synthetic_sixinone_dev_clean.json") 
    save_split(test_data, "synthetic_sixinone_test.json")  # New test file
    
    print("\n✅ Successfully created clean train/validation/test splits!")
    print("\nNew files created:")
    print("  - synthetic_sixinone_train_clean.json (10,800 examples)")
    print("  - synthetic_sixinone_dev_clean.json (600 examples)")
    print("  - synthetic_sixinone_test.json (600 examples)")
    print("\nNo data leakage between splits!")
    
    return True


if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)
