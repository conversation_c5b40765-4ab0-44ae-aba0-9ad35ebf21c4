#!/usr/bin/env python3
"""
Comprehensive training script for 6 separate Process Reward Models (PRMs) using ModernBERT.

This script trains 6 binary PRMs, one for each error type:
1. Calculation Error
2. Fabrication  
3. Context Inconsistency
4. Factual Inconsistency
5. Instruction Inconsistency
6. Logical Inconsistency

Each PRM is a binary classifier that predicts whether a step contains the specific error type.
Input format: question + y1 [SEP] y2 [SEP] ... where labels are placed on [SEP] tokens before step i.
Loss function: Step-wise PRM loss (same as standard PRM).
Inference: Sum the 6 PRM scores across steps during inference.
"""

import argparse
import json
import logging
import os
import sys
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Tuple, Union

import numpy as np
import torch
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import transformers
from transformers import (
    AutoConfig,
    AutoTokenizer,
    DataCollatorForTokenClassification,
    HfArgumentParser,
    PretrainedConfig,
    Trainer,
    TrainingArguments,
    set_seed,
)
import accelerate
import wandb
import yaml
from tqdm import tqdm

from my_modernbert import ModernBertForTokenClassification
from fgrlhf.utils import ensure_dir, set_seed as fgrlhf_set_seed

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Error type mappings
ERROR_TYPES = [
    "Calculation-Error",
    "Fabrication", 
    "Context-Inconsistency",
    "Factual-Inconsistency",
    "Instruction-Inconsistency",
    "Logical-Inconsistency"
]

ERROR_TYPE_TO_INDEX = {error_type: idx for idx, error_type in enumerate(ERROR_TYPES)}

# Label mappings for binary classification
IGNORE_TAG = "Ignore"
NO_ERROR_TAG = "O"
ERROR_TAG = "ERR"


@dataclass
class ModelArguments:
    """Arguments pertaining to which model/config/tokenizer we are going to fine-tune from."""

    model_name_or_path: str = field(
        default="answerdotai/ModernBERT-base",
        metadata={"help": "Path to pretrained model or model identifier from huggingface.co/models"}
    )
    config_name: Optional[str] = field(
        default=None, metadata={"help": "Pretrained config name or path if not the same as model_name"}
    )
    tokenizer_name: Optional[str] = field(
        default=None, metadata={"help": "Pretrained tokenizer name or path if not the same as model_name"}
    )
    cache_dir: Optional[str] = field(
        default=None,
        metadata={"help": "Where do you want to store the pretrained models downloaded from huggingface.co"},
    )
    model_revision: str = field(
        default="main",
        metadata={"help": "The specific model version to use (can be a branch name, tag name or commit id)."},
    )
    use_auth_token: bool = field(
        default=False,
        metadata={
            "help": (
                "Will use the token generated when running `huggingface-cli login` (necessary to use this script "
                "with private models)."
            )
        },
    )


@dataclass
class DataTrainingArguments:
    """Arguments pertaining to what data we are going to input our model for training and eval."""

    train_file: Optional[str] = field(default=None, metadata={"help": "A csv or a json file containing the training data."})
    validation_file: Optional[str] = field(default=None, metadata={"help": "A csv or a json file containing the validation data."})
    test_file: Optional[str] = field(default=None, metadata={"help": "A csv or a json file containing the test data."})
    input_column: Optional[str] = field(
        default="text", metadata={"help": "The column name of input text to classify."}
    )
    label_column: Optional[str] = field(
        default="feedback_tags", metadata={"help": "The column name of label to predict."}
    )
    max_seq_length: int = field(
        default=512,
        metadata={
            "help": (
                "The maximum total input sequence length after tokenization. Sequences longer "
                "than this will be truncated, sequences shorter will be padded."
            )
        },
    )
    overwrite_cache: bool = field(
        default=False, metadata={"help": "Overwrite the cached preprocessed datasets or not."}
    )
    pad_to_max_length: bool = field(
        default=False,
        metadata={
            "help": (
                "Whether to pad all samples to `max_seq_length`. "
                "If False, will pad the samples dynamically when batching to the maximum length in the batch."
            )
        },
    )
    preprocessing_num_workers: Optional[int] = field(
        default=None,
        metadata={"help": "The number of processes to use for the preprocessing."},
    )
    prediction_output_filename: str = field(
        default="predictions.txt",
        metadata={"help": "The filename to save predictions to."}
    )


@dataclass 
class PRMTrainingArguments:
    """Arguments specific to PRM training."""
    
    error_type: str = field(
        metadata={"help": "The specific error type to train for (e.g., 'Calculation-Error')"}
    )
    output_base_dir: str = field(
        default="../models/prm_modernbert",
        metadata={"help": "Base directory for saving trained models"}
    )
    positive_reward: float = field(
        default=1.0,
        metadata={"help": "Reward for correct predictions"}
    )
    negative_reward: float = field(
        default=-1.0, 
        metadata={"help": "Reward for incorrect predictions"}
    )
    sep_token: str = field(
        default="[SEP]",
        metadata={"help": "Separator token used in input format"}
    )


class PRMDataset(Dataset):
    """Dataset class for Process Reward Model training."""
    
    def __init__(self, data_file: str, error_type: str, tokenizer, max_length: int = 512):
        self.error_type = error_type
        self.tokenizer = tokenizer
        self.max_length = max_length
        
        # Load data
        with open(data_file, 'r') as f:
            self.data = [json.loads(line) for line in f]
        
        logger.info(f"Loaded {len(self.data)} examples for {error_type}")
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        text_tokens = item['text']
        feedback_tags = item['feedback_tags']
        
        # Convert to binary labels for specific error type
        binary_labels = self._convert_to_binary_labels(feedback_tags)
        
        return {
            'text': text_tokens,
            'labels': binary_labels
        }
    
    def _convert_to_binary_labels(self, feedback_tags: List[str]) -> List[str]:
        """Convert multi-class feedback tags to binary labels for specific error type."""
        binary_labels = []
        
        for tag in feedback_tags:
            if tag == IGNORE_TAG:
                binary_labels.append(IGNORE_TAG)
            elif tag == NO_ERROR_TAG:
                binary_labels.append(NO_ERROR_TAG)
            elif self.error_type in tag:  # e.g., "Calculation-Error" in "F-ERR" or specific error tag
                binary_labels.append(ERROR_TAG)
            else:
                binary_labels.append(NO_ERROR_TAG)  # Other error types treated as no error
                
        return binary_labels


def tokenize_and_align_labels(examples, tokenizer, label_to_id, max_seq_length, input_column, label_column):
    """Tokenize inputs and align labels with tokenized inputs."""
    tokenized_inputs = tokenizer(
        examples[input_column],
        padding="max_length",
        truncation=True,
        max_length=max_seq_length,
        is_split_into_words=True,
    )
    
    labels = []
    for i, label in enumerate(examples[label_column]):
        word_ids = tokenized_inputs.word_ids(batch_index=i)
        previous_word_idx = None
        label_ids = []
        
        for word_idx in word_ids:
            # Special tokens have a word id that is None. We set the label to -100 so they are automatically
            # ignored in the loss function.
            if word_idx is None:
                label_ids.append(-100)
            # We set the label for the first token of each word.
            elif word_idx != previous_word_idx:
                if label[word_idx] == IGNORE_TAG:
                    label_ids.append(-100)
                else:
                    label_ids.append(label_to_id[label[word_idx]])
            # For the other tokens in a word, we set the label to -100
            else:
                label_ids.append(-100)
            previous_word_idx = word_idx
            
        labels.append(label_ids)
    
    tokenized_inputs["labels"] = labels
    return tokenized_inputs


def compute_metrics(p, label_list):
    """Compute evaluation metrics."""
    predictions, labels = p
    predictions = np.argmax(predictions, axis=2)

    # Remove ignored index
    true_predictions = [
        [label_list[p] for (p, l) in zip(prediction, label) if l != -100]
        for prediction, label in zip(predictions, labels)
    ]
    true_labels = [
        [label_list[l] for (p, l) in zip(prediction, label) if l != -100]
        for prediction, label in zip(predictions, labels)
    ]

    # Calculate accuracy
    correct = 0
    total = 0
    for pred_seq, label_seq in zip(true_predictions, true_labels):
        for pred, label in zip(pred_seq, label_seq):
            if pred == label:
                correct += 1
            total += 1
    
    accuracy = correct / total if total > 0 else 0.0
    
    return {
        "accuracy": accuracy,
        "total_predictions": total
    }


def train_single_prm(error_type: str, model_args: ModelArguments, data_args: DataTrainingArguments,
                     training_args: TrainingArguments, prm_args: PRMTrainingArguments):
    """Train a single PRM for the specified error type."""

    logger.info(f"Training PRM for error type: {error_type}")

    # Set up output directory for this specific error type
    output_dir = os.path.join(prm_args.output_base_dir, error_type.replace("-", "_").lower())
    training_args.output_dir = output_dir
    ensure_dir(output_dir)

    # Load datasets
    from datasets import Dataset as HFDataset

    # Load and process training data
    train_data = []
    with open(data_args.train_file, 'r') as f:
        for line in f:
            item = json.loads(line)
            # Convert to binary labels for this error type
            binary_labels = convert_to_binary_labels(item['feedback_tags'], error_type)
            train_data.append({
                'text': item['text'],
                'feedback_tags': binary_labels
            })

    # Load and process validation data
    val_data = []
    with open(data_args.validation_file, 'r') as f:
        for line in f:
            item = json.loads(line)
            binary_labels = convert_to_binary_labels(item['feedback_tags'], error_type)
            val_data.append({
                'text': item['text'],
                'feedback_tags': binary_labels
            })

    # Create HuggingFace datasets
    train_dataset = HFDataset.from_list(train_data)
    eval_dataset = HFDataset.from_list(val_data)

    # Set up labels
    label_list = [ERROR_TAG, NO_ERROR_TAG]  # Binary classification
    label_to_id = {l: i for i, l in enumerate(label_list)}
    num_labels = len(label_list)

    # Load model and tokenizer
    config = AutoConfig.from_pretrained(
        model_args.config_name if model_args.config_name else model_args.model_name_or_path,
        num_labels=num_labels,
        cache_dir=model_args.cache_dir,
    )

    tokenizer = AutoTokenizer.from_pretrained(
        model_args.tokenizer_name if model_args.tokenizer_name else model_args.model_name_or_path,
        cache_dir=model_args.cache_dir,
        use_fast=True,
        add_prefix_space=True,
    )

    # Add [SEP] token if not present
    if prm_args.sep_token not in tokenizer.vocab:
        tokenizer.add_tokens([prm_args.sep_token])

    model = ModernBertForTokenClassification.from_pretrained(
        model_args.model_name_or_path,
        config=config,
        cache_dir=model_args.cache_dir
    )

    # Resize token embeddings if we added new tokens
    model.resize_token_embeddings(len(tokenizer))

    # Set label mappings in config
    model.config.label2id = label_to_id
    model.config.id2label = {i: l for i, l in enumerate(label_list)}

    # Tokenize datasets
    def tokenize_function(examples):
        return tokenize_and_align_labels(
            examples, tokenizer, label_to_id, data_args.max_seq_length,
            data_args.input_column, data_args.label_column
        )

    train_dataset = train_dataset.map(
        tokenize_function,
        batched=True,
        num_proc=data_args.preprocessing_num_workers,
        load_from_cache_file=not data_args.overwrite_cache,
        desc="Running tokenizer on train dataset",
    )

    eval_dataset = eval_dataset.map(
        tokenize_function,
        batched=True,
        num_proc=data_args.preprocessing_num_workers,
        load_from_cache_file=not data_args.overwrite_cache,
        desc="Running tokenizer on validation dataset",
    )

    # Data collator
    data_collator = DataCollatorForTokenClassification(
        tokenizer, pad_to_multiple_of=8 if training_args.fp16 else None
    )

    # Metrics function
    def compute_metrics_fn(p):
        return compute_metrics(p, label_list)

    # Initialize trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=eval_dataset,
        tokenizer=tokenizer,
        data_collator=data_collator,
        compute_metrics=compute_metrics_fn,
    )

    # Train
    logger.info(f"Starting training for {error_type}")
    train_result = trainer.train()

    # Save model
    trainer.save_model()

    # Save training metrics
    metrics = train_result.metrics
    trainer.log_metrics("train", metrics)
    trainer.save_metrics("train", metrics)
    trainer.save_state()

    # Evaluate
    logger.info(f"Evaluating {error_type}")
    eval_metrics = trainer.evaluate()
    trainer.log_metrics("eval", eval_metrics)
    trainer.save_metrics("eval", eval_metrics)

    logger.info(f"Completed training for {error_type}")
    return trainer, eval_metrics


def convert_to_binary_labels(feedback_tags: List[str], error_type: str) -> List[str]:
    """Convert multi-class feedback tags to binary labels for specific error type."""
    binary_labels = []

    for tag in feedback_tags:
        if tag == IGNORE_TAG:
            binary_labels.append(IGNORE_TAG)
        elif tag == NO_ERROR_TAG:
            binary_labels.append(NO_ERROR_TAG)
        elif error_type.replace("-", "_").upper() in tag.upper() or f"{error_type.split('-')[0]}-ERR" == tag:
            binary_labels.append(ERROR_TAG)
        else:
            binary_labels.append(NO_ERROR_TAG)  # Other error types treated as no error

    return binary_labels


def main():
    """Main training function that trains all 6 PRMs."""

    # Parse arguments
    parser = HfArgumentParser((ModelArguments, DataTrainingArguments, TrainingArguments, PRMTrainingArguments))

    if len(sys.argv) == 2 and sys.argv[1].endswith(".json"):
        # If we pass only one argument to the script and it's the path to a json file,
        # let's parse it to get our arguments.
        model_args, data_args, training_args, prm_args = parser.parse_json_file(json_file=os.path.abspath(sys.argv[1]))
    else:
        model_args, data_args, training_args, prm_args = parser.parse_args_into_dataclasses()

    # Setup logging
    logging.basicConfig(
        format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
        datefmt="%m/%d/%Y %H:%M:%S",
        handlers=[logging.StreamHandler(sys.stdout)],
    )

    log_level = training_args.get_process_log_level()
    logger.setLevel(log_level)
    transformers.utils.logging.set_verbosity(log_level)
    transformers.utils.logging.enable_default_handler()
    transformers.utils.logging.enable_explicit_format()

    # Log on each process the small summary:
    logger.warning(
        f"Process rank: {training_args.local_rank}, device: {training_args.device}, n_gpu: {training_args.n_gpu}"
        + f"distributed training: {bool(training_args.local_rank != -1)}, 16-bits training: {training_args.fp16}"
    )
    logger.info(f"Training/evaluation parameters {training_args}")

    # Set seed before initializing model.
    set_seed(training_args.seed)

    # Initialize wandb for experiment tracking
    if training_args.report_to and "wandb" in training_args.report_to:
        import wandb
        wandb.init(
            project="FG-PRM-ModernBERT",
            name=f"six_prms_training_{training_args.run_name or 'default'}",
            config={
                "model_args": model_args.__dict__,
                "data_args": data_args.__dict__,
                "training_args": training_args.__dict__,
                "prm_args": prm_args.__dict__,
            }
        )

    # Train each PRM separately
    results = {}
    trained_models = {}

    for error_type in ERROR_TYPES:
        logger.info(f"\n{'='*50}")
        logger.info(f"Training PRM for: {error_type}")
        logger.info(f"{'='*50}")

        try:
            # Create a copy of training args for this specific model
            current_training_args = TrainingArguments(
                output_dir=os.path.join(prm_args.output_base_dir, error_type.replace("-", "_").lower()),
                overwrite_output_dir=training_args.overwrite_output_dir,
                do_train=training_args.do_train,
                do_eval=training_args.do_eval,
                do_predict=training_args.do_predict,
                evaluation_strategy=training_args.evaluation_strategy,
                eval_steps=training_args.eval_steps,
                per_device_train_batch_size=training_args.per_device_train_batch_size,
                per_device_eval_batch_size=training_args.per_device_eval_batch_size,
                gradient_accumulation_steps=training_args.gradient_accumulation_steps,
                learning_rate=training_args.learning_rate,
                weight_decay=training_args.weight_decay,
                adam_beta1=training_args.adam_beta1,
                adam_beta2=training_args.adam_beta2,
                adam_epsilon=training_args.adam_epsilon,
                max_grad_norm=training_args.max_grad_norm,
                num_train_epochs=training_args.num_train_epochs,
                max_steps=training_args.max_steps,
                lr_scheduler_type=training_args.lr_scheduler_type,
                warmup_ratio=training_args.warmup_ratio,
                warmup_steps=training_args.warmup_steps,
                logging_dir=os.path.join(training_args.logging_dir or "logs", error_type.replace("-", "_").lower()),
                logging_strategy=training_args.logging_strategy,
                logging_steps=training_args.logging_steps,
                save_strategy=training_args.save_strategy,
                save_steps=training_args.save_steps,
                save_total_limit=training_args.save_total_limit,
                seed=training_args.seed,
                fp16=training_args.fp16,
                bf16=training_args.bf16,
                local_rank=training_args.local_rank,
                dataloader_drop_last=training_args.dataloader_drop_last,
                dataloader_num_workers=training_args.dataloader_num_workers,
                load_best_model_at_end=training_args.load_best_model_at_end,
                metric_for_best_model=training_args.metric_for_best_model,
                greater_is_better=training_args.greater_is_better,
                report_to=training_args.report_to,
                run_name=f"{training_args.run_name}_{error_type}" if training_args.run_name else error_type,
            )

            # Update PRM args for current error type
            current_prm_args = PRMTrainingArguments(
                error_type=error_type,
                output_base_dir=prm_args.output_base_dir,
                positive_reward=prm_args.positive_reward,
                negative_reward=prm_args.negative_reward,
                sep_token=prm_args.sep_token,
            )

            # Train the PRM
            trainer, eval_metrics = train_single_prm(
                error_type, model_args, data_args, current_training_args, current_prm_args
            )

            results[error_type] = eval_metrics
            trained_models[error_type] = trainer.model

            logger.info(f"Completed {error_type}: Eval Accuracy = {eval_metrics.get('eval_accuracy', 'N/A')}")

        except Exception as e:
            logger.error(f"Failed to train PRM for {error_type}: {str(e)}")
            results[error_type] = {"error": str(e)}

    # Save overall results
    overall_results_path = os.path.join(prm_args.output_base_dir, "overall_results.json")
    with open(overall_results_path, 'w') as f:
        json.dump(results, f, indent=2)

    # Log summary
    logger.info(f"\n{'='*50}")
    logger.info("TRAINING SUMMARY")
    logger.info(f"{'='*50}")

    for error_type, metrics in results.items():
        if "error" in metrics:
            logger.info(f"{error_type}: FAILED - {metrics['error']}")
        else:
            accuracy = metrics.get('eval_accuracy', 'N/A')
            logger.info(f"{error_type}: Accuracy = {accuracy}")

    logger.info(f"Results saved to: {overall_results_path}")
    logger.info("Training completed!")

    if training_args.report_to and "wandb" in training_args.report_to:
        wandb.log({"final_results": results})
        wandb.finish()


if __name__ == "__main__":
    main()
