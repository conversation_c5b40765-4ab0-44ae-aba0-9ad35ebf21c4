# FG-PRM Project Structure

This document describes the clean, organized structure of the Fine-Grained Process Reward Model (FG-PRM) project with both Longformer baseline and ModernBERT implementations.

## 📁 Project Overview

```
FG-PRM/
├── 📄 README.md                           # Main project documentation
├── 📄 LICENSE                             # Project license
├── 📄 README_ModernBERT_Training.md       # ModernBERT training guide
├── 📄 MODERNBERT_IMPLEMENTATION_SUMMARY.md # ModernBERT implementation summary
├── 📄 PROJECT_STRUCTURE.md                # This file
├── 📁 configs/                            # Configuration files
├── 📁 data/                               # Training and test data
├── 📁 models/                             # Trained model outputs
├── 📁 scripts/                            # Automation scripts
└── 📁 src/                                # Source code
```

## 🔧 Core Components

### 1. **Longformer Baseline Implementation**

#### Model Architecture
- **`src/reward_modeling/my_longformer.py`** - Core Longformer model implementations
  - `LongformerForTokenClassification` - Single error type classification
  - `LongformerForTokenMultiClassification` - Multi-error type classification

#### Training Scripts
- **`src/reward_modeling/run_fg_rm.py`** - Main Longformer training script
- **`src/reward_modeling/run_fg_rm_six.py`** - Six-model training variant
- **`src/reward_modeling/rpe_train_fact_rm_longformer.sh`** - Training shell script

#### Verification
- **`src/verification/verify_longformer.py`** - Main Longformer verification
- **`src/verification/verify_longformer_single.py`** - Single model verification
- **`src/verification/reward.py`** - Longformer reward system

#### Fine-tuning
- **`src/fine-tuning/my_longformer.py`** - Longformer fine-tuning models
- **`src/fine-tuning/train_finegrained.py`** - Fine-grained training script
- **`src/fine-tuning/reward.py`** - Fine-tuning reward system

### 2. **ModernBERT Implementation**

#### Model Architecture
- **`src/fine-tuning/my_modernbert.py`** - Core ModernBERT model implementations
  - `ModernBertForTokenClassification` - Binary classification for individual error types
  - `ModernBertForTokenMultiClassification` - Multi-class variant

#### Training Scripts
- **`src/fine-tuning/train_modernbert_six_prms.py`** - Complete 6-model training system
- **`src/reward_modeling/run_fg_rm_modernbert.py`** - ModernBERT reward modeling
- **`src/reward_modeling/rpe_train_fact_rm_modernbert.sh`** - Training shell script

#### Verification
- **`src/verification/verify_modernbert.py`** - ModernBERT verification system

#### Reward System
- **`src/fine-tuning/reward_modernbert.py`** - Complete ModernBERT reward integration
  - `ModernBertFactualityReward` - Individual PRM reward computation
  - `ModernBertFineGrainedReward` - Aggregated 6-PRM system

#### Automation
- **`scripts/train_all_modernbert_prms.sh`** - Complete training automation
- **`scripts/run_modernbert_verification.sh`** - Verification automation
- **`scripts/test_modernbert_integration.py`** - Integration testing

#### Configuration
- **`configs/train_modernbert_six_prms.json`** - Training configuration

### 3. **Shared Components**

#### Core Framework
- **`src/fgrlhf/`** - Fine-Grained RLHF framework
  - `reward.py` - Base reward classes
  - `policy.py` - Policy implementations
  - `ppo.py` - PPO training logic
  - `utils.py` - Utility functions

#### Utilities
- **`src/utils/`** - Shared utilities
  - `prompt.py` - Prompt generation utilities
  - `hallucination_prompt.py` - Hallucination-specific prompts

#### Analysis Tools
- **`src/analysis/`** - Analysis and evaluation tools
  - `human_eval.py` - Human evaluation scripts
  - `evaluate_reasoning_steps.py` - Step-wise evaluation

#### Data Generation
- **`src/synthetic/`** - Synthetic data generation
- **`src/generation/`** - Answer generation utilities

## 📊 Data Structure

```
data/
└── hallucination_sample/
    ├── synthetic_*_train.json          # Training data by error type
    ├── synthetic_*_dev.json            # Development data by error type
    └── synthetic_*_test.json           # Test data by error type
```

**Error Types Supported:**
- `Calculation-Error` - Mathematical calculation mistakes
- `Fabrication` - Made-up or false information
- `Context-Inconsistency` - Inconsistencies with given context
- `Factual-Inconsistency` - Factual errors
- `Instruction-Inconsistency` - Deviations from instructions
- `Logical-Inconsistency` - Logical reasoning errors

## 🎯 Model Outputs

```
models/
├── prm_longformer/                     # Longformer trained models
│   ├── calculation_error/
│   ├── fabrication/
│   └── ...
└── prm_modernbert/                     # ModernBERT trained models
    ├── calculation_error/
    ├── fabrication/
    ├── context_inconsistency/
    ├── factual_inconsistency/
    ├── instruction_inconsistency/
    └── logical_inconsistency/
```

## 🚀 Quick Start

### Longformer Baseline
```bash
# Train Longformer model
cd src/reward_modeling
./rpe_train_fact_rm_longformer.sh

# Run verification
cd ../verification
python verify_longformer.py
```

### ModernBERT Implementation
```bash
# Train all 6 ModernBERT PRMs
./scripts/train_all_modernbert_prms.sh

# Run verification
./scripts/run_modernbert_verification.sh

# Test integration
./scripts/test_modernbert_integration.py
```

## 🔄 Key Differences

| **Aspect** | **Longformer Baseline** | **ModernBERT Implementation** |
|------------|------------------------|-------------------------------|
| **Base Model** | `allenai/longformer-base-4096` | `answerdotai/ModernBERT-base` |
| **Architecture** | Single multi-class model | 6 separate binary models |
| **Training** | One training run | 6 separate training runs |
| **Inference** | Single model prediction | Sum of 6 PRM scores |
| **Error Types** | Mixed classification | Dedicated binary classifiers |
| **Efficiency** | Slower, memory-intensive | Faster, more efficient |
| **Interpretability** | Mixed error signals | Clear per-error-type signals |

## 📝 Configuration Files

- **`src/config.yml`** - Main project configuration
- **`src/fine-tuning/baseline_config.yml`** - Baseline training config
- **`src/fine-tuning/fine_grained_config.yml`** - Fine-grained training config
- **`configs/train_modernbert_six_prms.json`** - ModernBERT training config

## 🧪 Testing and Validation

- **Integration Tests**: `scripts/test_modernbert_integration.py`
- **Verification Scripts**: Both Longformer and ModernBERT verification
- **Analysis Tools**: Comprehensive evaluation and comparison utilities

## 📚 Documentation

- **`README.md`** - Main project overview
- **`README_ModernBERT_Training.md`** - Detailed ModernBERT training guide
- **`MODERNBERT_IMPLEMENTATION_SUMMARY.md`** - Implementation summary
- **`src/reward_modeling/README_ModernBERT.md`** - ModernBERT reward modeling guide

## 🎯 Key Features

### Longformer Baseline
- ✅ Proven architecture for long sequences
- ✅ Multi-class error detection
- ✅ Established training pipeline
- ✅ Compatible with existing systems

### ModernBERT Implementation
- ✅ 6 separate binary PRMs for each error type
- ✅ More efficient training and inference
- ✅ Better interpretability per error type
- ✅ Modern architecture with improved performance
- ✅ Complete automation and testing suite
- ✅ Drop-in replacement for existing verification

This clean structure provides two complete, functional implementations that can be easily compared, maintained, and extended.
