#!/usr/bin/env python3
"""
Utility script to convert token-level classification data to binary sequence classification format.
This is useful for creating binary classification datasets from existing token-level data.
"""

import json
import argparse
from pathlib import Path
from typing import List, Dict, Any


def convert_token_to_binary_label(feedback_tags: List[str]) -> int:
    """
    Convert token-level feedback tags to binary sequence-level label.

    Handles all error types from the comprehensive sixinone dataset:
    - Calculation-Error: Mathematical computation errors
    - Context-Inconsistency: Information inconsistent with context
    - Fabrication: Made-up or false information
    - Factual-Inconsistency: Incorrect factual claims
    - Instruction-Inconsistency: Response doesn't follow instructions
    - Logical-Inconsistency: Logical reasoning errors
    - F-ERR: Generic error tag

    Args:
        feedback_tags: List of token-level feedback tags

    Returns:
        Binary label: 0 = no error, 1 = has any type of error
    """
    # Define all known error types
    ERROR_TYPES = {
        "Calculation-Error",
        "Context-Inconsistency",
        "Fabrication",
        "Factual-Inconsistency",
        "Instruction-Inconsistency",
        "Logical-Inconsistency",
        "F-ERR"  # Generic error tag
    }

    # Check if any token has an error tag (not "O" or "Ignore")
    has_error = any(tag in ERROR_TYPES for tag in feedback_tags)
    return 1 if has_error else 0


def convert_dataset_to_binary(input_file: str, output_file: str, text_column: str = "text", label_column: str = "feedback_tags"):
    """
    Convert a token-level classification dataset to binary sequence classification format.
    
    Args:
        input_file: Path to input JSON file with token-level data
        output_file: Path to output JSON file for binary classification
        text_column: Name of the column containing text tokens
        label_column: Name of the column containing feedback tags
    """
    converted_data = []
    
    with open(input_file, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            try:
                data = json.loads(line.strip())
                
                # Extract text and labels
                text_tokens = data.get(text_column, [])
                feedback_tags = data.get(label_column, [])
                
                if not text_tokens or not feedback_tags:
                    print(f"Warning: Empty text or labels in line {line_num}, skipping...")
                    continue
                
                if len(text_tokens) != len(feedback_tags):
                    print(f"Warning: Mismatched text and label lengths in line {line_num}, skipping...")
                    continue
                
                # Convert to binary format
                text = " ".join(text_tokens)
                binary_label = convert_token_to_binary_label(feedback_tags)
                
                converted_item = {
                    "id": data.get("id", f"item_{line_num}"),
                    "text": text,
                    "label": binary_label,
                    "original_tokens": text_tokens,
                    "original_feedback_tags": feedback_tags
                }
                
                converted_data.append(converted_item)
                
            except json.JSONDecodeError as e:
                print(f"Error parsing JSON in line {line_num}: {e}")
                continue
            except Exception as e:
                print(f"Error processing line {line_num}: {e}")
                continue
    
    # Write converted data
    with open(output_file, 'w', encoding='utf-8') as f:
        for item in converted_data:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')
    
    print(f"Converted {len(converted_data)} examples from {input_file} to {output_file}")
    
    # Print statistics
    label_counts = {}
    for item in converted_data:
        label = item['label']
        label_counts[label] = label_counts.get(label, 0) + 1
    
    print("Label distribution:")
    for label, count in sorted(label_counts.items()):
        label_name = "No Error" if label == 0 else "Has Error"
        percentage = (count / len(converted_data)) * 100
        print(f"  {label_name} ({label}): {count} ({percentage:.1f}%)")


def convert_multiple_files(input_pattern: str, output_dir: str, text_column: str = "text", label_column: str = "feedback_tags"):
    """
    Convert multiple files matching a pattern.
    
    Args:
        input_pattern: Glob pattern for input files
        output_dir: Directory to save converted files
        text_column: Name of the column containing text tokens
        label_column: Name of the column containing feedback tags
    """
    from glob import glob
    
    input_files = glob(input_pattern)
    if not input_files:
        print(f"No files found matching pattern: {input_pattern}")
        return
    
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    for input_file in input_files:
        input_path = Path(input_file)
        output_file = output_path / f"binary_{input_path.name}"
        
        print(f"\nConverting {input_file} -> {output_file}")
        convert_dataset_to_binary(input_file, str(output_file), text_column, label_column)


def main():
    parser = argparse.ArgumentParser(
        description="Convert token-level classification data to binary sequence classification format"
    )
    parser.add_argument(
        "input", 
        help="Input file path or glob pattern"
    )
    parser.add_argument(
        "output", 
        help="Output file path or directory (for multiple files)"
    )
    parser.add_argument(
        "--text-column", 
        default="text", 
        help="Name of the column containing text tokens (default: text)"
    )
    parser.add_argument(
        "--label-column", 
        default="feedback_tags", 
        help="Name of the column containing feedback tags (default: feedback_tags)"
    )
    parser.add_argument(
        "--multiple", 
        action="store_true", 
        help="Process multiple files matching input pattern"
    )
    
    args = parser.parse_args()
    
    if args.multiple:
        convert_multiple_files(args.input, args.output, args.text_column, args.label_column)
    else:
        convert_dataset_to_binary(args.input, args.output, args.text_column, args.label_column)


if __name__ == "__main__":
    main()
