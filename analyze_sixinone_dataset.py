#!/usr/bin/env python3
"""
Comprehensive analysis script for the sixinone multi-error dataset.
This script analyzes the dataset structure, label distribution, and validates
the binary classification conversion approach.
"""

import json
import sys
from collections import Counter, defaultdict
from pathlib import Path


def analyze_dataset_structure(file_path):
    """Analyze the structure and content of the sixinone dataset."""
    print(f"Analyzing dataset: {Path(file_path).name}")
    print("=" * 60)
    
    # Counters for analysis
    label_counts = Counter()
    error_type_counts = Counter()
    example_stats = {
        'total_examples': 0,
        'examples_with_errors': 0,
        'examples_without_errors': 0,
        'total_tokens': 0,
        'error_tokens': 0,
        'ignore_tokens': 0,
        'o_tokens': 0
    }
    
    # Track examples by error type combination
    error_combinations = Counter()
    
    # Define error types
    ERROR_TYPES = {
        "Calculation-Error",
        "Context-Inconsistency", 
        "Fabrication",
        "Factual-Inconsistency",
        "Instruction-Inconsistency",
        "Logical-Inconsistency",
        "F-ERR"
    }
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if line_num % 1000 == 0:
                    print(f"  Processing example {line_num}...")
                
                try:
                    data = json.loads(line.strip())
                    feedback_tags = data.get('feedback_tags', [])
                    text_tokens = data.get('text', [])
                    
                    example_stats['total_examples'] += 1
                    example_stats['total_tokens'] += len(feedback_tags)
                    
                    # Count all labels
                    for tag in feedback_tags:
                        label_counts[tag] += 1
                        if tag == 'O':
                            example_stats['o_tokens'] += 1
                        elif tag == 'Ignore':
                            example_stats['ignore_tokens'] += 1
                        elif tag in ERROR_TYPES:
                            example_stats['error_tokens'] += 1
                            error_type_counts[tag] += 1
                    
                    # Check what error types are present in this example
                    example_errors = set(tag for tag in feedback_tags if tag in ERROR_TYPES)
                    
                    if example_errors:
                        example_stats['examples_with_errors'] += 1
                        # Track error type combinations
                        error_combo = tuple(sorted(example_errors))
                        error_combinations[error_combo] += 1
                    else:
                        example_stats['examples_without_errors'] += 1
                        
                except json.JSONDecodeError as e:
                    print(f"  Warning: JSON decode error at line {line_num}: {e}")
                    continue
                except Exception as e:
                    print(f"  Warning: Error processing line {line_num}: {e}")
                    continue
    
    except FileNotFoundError:
        print(f"Error: File not found: {file_path}")
        return None
    
    return {
        'label_counts': label_counts,
        'error_type_counts': error_type_counts,
        'example_stats': example_stats,
        'error_combinations': error_combinations,
        'error_types': ERROR_TYPES
    }


def print_analysis_results(analysis):
    """Print comprehensive analysis results."""
    if not analysis:
        return
    
    stats = analysis['example_stats']
    label_counts = analysis['label_counts']
    error_type_counts = analysis['error_type_counts']
    error_combinations = analysis['error_combinations']
    
    print("\n📊 DATASET OVERVIEW")
    print("-" * 40)
    print(f"Total examples: {stats['total_examples']:,}")
    print(f"Total tokens: {stats['total_tokens']:,}")
    print(f"Examples with errors: {stats['examples_with_errors']:,} ({stats['examples_with_errors']/stats['total_examples']*100:.1f}%)")
    print(f"Examples without errors: {stats['examples_without_errors']:,} ({stats['examples_without_errors']/stats['total_examples']*100:.1f}%)")
    
    print("\n🏷️  TOKEN-LEVEL LABEL DISTRIBUTION")
    print("-" * 40)
    for label, count in label_counts.most_common():
        percentage = count / stats['total_tokens'] * 100
        print(f"{label:25}: {count:8,} ({percentage:5.1f}%)")
    
    print("\n🚨 ERROR TYPE DISTRIBUTION")
    print("-" * 40)
    for error_type, count in error_type_counts.most_common():
        percentage = count / stats['error_tokens'] * 100 if stats['error_tokens'] > 0 else 0
        print(f"{error_type:25}: {count:6,} ({percentage:5.1f}% of error tokens)")
    
    print("\n🔄 BINARY CLASSIFICATION CONVERSION")
    print("-" * 40)
    print("Conversion strategy:")
    print("  0 (No Error): Only 'O' and 'Ignore' tags")
    print("  1 (Has Error): Any of the 6+ error types present")
    print()
    print("Expected binary distribution:")
    print(f"  Class 0 (No Error): {stats['examples_without_errors']:,} examples ({stats['examples_without_errors']/stats['total_examples']*100:.1f}%)")
    print(f"  Class 1 (Has Error): {stats['examples_with_errors']:,} examples ({stats['examples_with_errors']/stats['total_examples']*100:.1f}%)")
    
    print("\n🔗 ERROR TYPE COMBINATIONS")
    print("-" * 40)
    print("Top 10 error type combinations in examples:")
    for combo, count in error_combinations.most_common(10):
        if combo:  # Skip empty combinations
            combo_str = " + ".join(combo) if len(combo) > 1 else combo[0]
            percentage = count / stats['examples_with_errors'] * 100
            print(f"  {combo_str:40}: {count:4,} ({percentage:4.1f}%)")
    
    print("\n✅ VALIDATION SUMMARY")
    print("-" * 40)
    print(f"✓ Dataset contains {len(error_type_counts)} distinct error types")
    print(f"✓ Binary classification will have {stats['examples_with_errors']/stats['total_examples']*100:.1f}% positive examples")
    print(f"✓ Dataset is {'balanced' if 0.3 <= stats['examples_with_errors']/stats['total_examples'] <= 0.7 else 'imbalanced'}")
    
    # Check for potential issues
    if stats['examples_with_errors']/stats['total_examples'] > 0.9:
        print("⚠ Warning: Very high positive class ratio - consider class balancing")
    elif stats['examples_with_errors']/stats['total_examples'] < 0.1:
        print("⚠ Warning: Very low positive class ratio - consider class balancing")


def validate_binary_conversion(file_path, sample_size=100):
    """Validate the binary conversion with sample examples."""
    print(f"\n🧪 BINARY CONVERSION VALIDATION")
    print("-" * 40)
    
    ERROR_TYPES = {
        "Calculation-Error", "Context-Inconsistency", "Fabrication",
        "Factual-Inconsistency", "Instruction-Inconsistency", 
        "Logical-Inconsistency", "F-ERR"
    }
    
    def convert_to_binary(feedback_tags):
        return 1 if any(tag in ERROR_TYPES for tag in feedback_tags) else 0
    
    validation_results = []
    
    with open(file_path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            if len(validation_results) >= sample_size:
                break
                
            try:
                data = json.loads(line.strip())
                feedback_tags = data.get('feedback_tags', [])
                
                # Convert to binary
                binary_label = convert_to_binary(feedback_tags)
                
                # Find error types in this example
                example_errors = [tag for tag in feedback_tags if tag in ERROR_TYPES]
                unique_errors = list(set(example_errors))
                
                validation_results.append({
                    'id': data.get('id', f'example_{line_num}'),
                    'binary_label': binary_label,
                    'error_types': unique_errors,
                    'error_count': len(example_errors),
                    'total_tokens': len(feedback_tags)
                })
                
            except Exception as e:
                continue
    
    # Print validation results
    print(f"Validated {len(validation_results)} examples:")
    
    binary_dist = Counter(r['binary_label'] for r in validation_results)
    print(f"  Binary distribution: {dict(binary_dist)}")
    
    # Show examples with errors
    error_examples = [r for r in validation_results if r['binary_label'] == 1]
    if error_examples:
        print(f"\nSample examples with errors (showing first 5):")
        for i, example in enumerate(error_examples[:5]):
            print(f"  {i+1}. ID: {example['id'][:20]}...")
            print(f"     Error types: {example['error_types']}")
            print(f"     Error tokens: {example['error_count']}/{example['total_tokens']}")
    
    # Show error type distribution in sample
    all_error_types = []
    for r in validation_results:
        all_error_types.extend(r['error_types'])
    
    if all_error_types:
        error_dist = Counter(all_error_types)
        print(f"\nError type distribution in sample:")
        for error_type, count in error_dist.most_common():
            print(f"  {error_type}: {count}")


def main():
    """Main analysis function."""
    print("🔍 SIXINONE DATASET ANALYSIS FOR BINARY CLASSIFICATION")
    print("=" * 80)
    
    # File paths
    train_file = "/mnt/c/Projects/FG-PRM/data/hallucination_sample/synthetic_sixinone_train.json"
    dev_file = "/mnt/c/Projects/FG-PRM/data/hallucination_sample/synthetic_sixinone_dev.json"
    
    # Analyze training dataset
    print("🚂 TRAINING DATASET ANALYSIS")
    train_analysis = analyze_dataset_structure(train_file)
    if train_analysis:
        print_analysis_results(train_analysis)
    
    print("\n" + "=" * 80)
    
    # Analyze development dataset  
    print("🔬 DEVELOPMENT DATASET ANALYSIS")
    dev_analysis = analyze_dataset_structure(dev_file)
    if dev_analysis:
        print_analysis_results(dev_analysis)
    
    # Validate conversion approach
    if train_analysis:
        validate_binary_conversion(train_file, sample_size=50)
    
    print("\n" + "=" * 80)
    print("📋 RECOMMENDATIONS FOR MODERNBERT TRAINING")
    print("-" * 40)
    
    if train_analysis and dev_analysis:
        train_pos_ratio = train_analysis['example_stats']['examples_with_errors'] / train_analysis['example_stats']['total_examples']
        dev_pos_ratio = dev_analysis['example_stats']['examples_with_errors'] / dev_analysis['example_stats']['total_examples']
        
        print(f"✓ Use sixinone dataset for comprehensive multi-error training")
        print(f"✓ Binary classification approach is appropriate")
        print(f"✓ Training set: {train_analysis['example_stats']['total_examples']:,} examples")
        print(f"✓ Dev set: {dev_analysis['example_stats']['total_examples']:,} examples")
        print(f"✓ Positive class ratio: {train_pos_ratio:.1%} (train), {dev_pos_ratio:.1%} (dev)")
        
        if abs(train_pos_ratio - dev_pos_ratio) > 0.05:
            print("⚠ Warning: Train/dev positive class ratios differ significantly")
        
        # Recommend training parameters
        print(f"\nRecommended training parameters:")
        print(f"  --train_file ../data/hallucination_sample/synthetic_sixinone_train.json")
        print(f"  --validation_file ../data/hallucination_sample/synthetic_sixinone_dev.json")
        print(f"  --per_device_train_batch_size 4  # Large dataset, start conservative")
        print(f"  --num_train_epochs 20-30  # Large dataset needs fewer epochs")
        print(f"  --learning_rate 1e-5  # Standard for ModernBERT")
        print(f"  --metric_for_best_model f1  # Better for imbalanced data")
        print(f"  --gradient_accumulation_steps 2  # Effective batch size = 8")


if __name__ == "__main__":
    main()
