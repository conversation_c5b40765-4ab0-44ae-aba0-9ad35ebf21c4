#!/usr/bin/env python3
"""
Script to analyze duplicate IDs in a specific JSON file.
"""

import json
import os
from collections import Counter

def analyze_duplicate_ids(file_path):
    """
    Analyze duplicate IDs in a JSON lines file.
    
    Args:
        file_path (str): Path to the JSON lines file
    
    Returns:
        dict: Analysis results including counts and duplicate information
    """
    if not os.path.exists(file_path):
        return {"error": f"File not found: {file_path}"}
    
    print(f"Analyzing file: {file_path}")
    print(f"File size: {os.path.getsize(file_path) / (1024*1024):.1f} MB")
    
    ids = []
    total_entries = 0
    parse_errors = 0
    
    # Read all IDs from the file
    with open(file_path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if not line:
                continue
                
            try:
                entry = json.loads(line)
                total_entries += 1
                
                entry_id = entry.get('id')
                if entry_id is None:
                    print(f"Warning: Entry at line {line_num} has no 'id' field")
                    continue
                    
                ids.append(entry_id)
                
            except json.JSONDecodeError as e:
                parse_errors += 1
                print(f"Error parsing JSON at line {line_num}: {e}")
                continue
    
    # Count occurrences of each ID
    id_counts = Counter(ids)
    
    # Find duplicates
    duplicates = {id_val: count for id_val, count in id_counts.items() if count > 1}
    unique_ids = len(id_counts)
    total_duplicate_entries = sum(count - 1 for count in duplicates.values())
    
    # Analysis results
    results = {
        "file_path": file_path,
        "total_entries": total_entries,
        "parse_errors": parse_errors,
        "total_ids_found": len(ids),
        "unique_ids": unique_ids,
        "duplicate_ids_count": len(duplicates),
        "total_duplicate_entries": total_duplicate_entries,
        "duplicates": duplicates
    }
    
    return results

def main():
    """Main function to analyze the Context-Inconsistency file."""
    
    # Target file
    file_path = "data/hallucination_sample/synthetic_Context-Inconsistency_train.json"
    
    print("=== Duplicate ID Analysis ===\n")
    
    # Analyze the file
    results = analyze_duplicate_ids(file_path)
    
    if "error" in results:
        print(f"Error: {results['error']}")
        return
    
    # Print summary
    print(f"\n=== Analysis Results ===")
    print(f"File: {os.path.basename(results['file_path'])}")
    print(f"Total entries in file: {results['total_entries']:,}")
    print(f"Parse errors: {results['parse_errors']}")
    print(f"Total IDs found: {results['total_ids_found']:,}")
    print(f"Unique IDs: {results['unique_ids']:,}")
    print(f"Number of IDs that appear multiple times: {results['duplicate_ids_count']:,}")
    print(f"Total duplicate entries (extra occurrences): {results['total_duplicate_entries']:,}")
    
    if results['total_duplicate_entries'] > 0:
        duplication_rate = (results['total_duplicate_entries'] / results['total_entries']) * 100
        print(f"Duplication rate: {duplication_rate:.2f}%")
        
        # Show some examples of duplicates
        print(f"\n=== Sample Duplicate IDs ===")
        sample_duplicates = list(results['duplicates'].items())[:10]  # Show first 10
        for id_val, count in sample_duplicates:
            print(f"  ID '{id_val}': appears {count} times")
        
        if len(results['duplicates']) > 10:
            print(f"  ... and {len(results['duplicates']) - 10} more duplicate IDs")
        
        # Show distribution of duplicate counts
        print(f"\n=== Duplicate Count Distribution ===")
        count_distribution = Counter(results['duplicates'].values())
        for dup_count, num_ids in sorted(count_distribution.items()):
            print(f"  {num_ids} IDs appear {dup_count} times each")
    else:
        print(f"\n✅ No duplicate IDs found - all entries have unique IDs!")
    
    print(f"\n=== Summary ===")
    if results['total_duplicate_entries'] > 0:
        print(f"❌ Found {results['total_duplicate_entries']:,} duplicate entries")
        print(f"   ({results['duplicate_ids_count']:,} unique IDs appear multiple times)")
    else:
        print(f"✅ No duplicates found - file has clean, unique IDs")

if __name__ == "__main__":
    main()
