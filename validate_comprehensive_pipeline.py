#!/usr/bin/env python3
"""
Validation script for the comprehensive multi-error ModernBERT pipeline.
Tests the complete pipeline with the sixinone dataset.
"""

import json
import os
from pathlib import Path
from collections import Counter


def validate_comprehensive_pipeline():
    """Validate the complete ModernBERT pipeline with sixinone dataset."""
    print("🔍 COMPREHENSIVE MODERNBERT PIPELINE VALIDATION")
    print("=" * 70)
    
    # Check required files
    print("1. Checking pipeline files...")
    required_files = [
        "rpe_train_fact_rm_modernbert.sh",
        "run_fg_rm_modernbert.py", 
        "eval_binary.py",
        "convert_to_binary.py",
        "README_ModernBERT.md"
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✓ {file}")
        else:
            missing_files.append(file)
            print(f"   ❌ {file}")
    
    if missing_files:
        print(f"\n❌ Missing files: {missing_files}")
        return False
    
    # Check data files
    print("\n2. Checking sixinone dataset files...")
    data_files = [
        "/mnt/c/Projects/FG-PRM/data/hallucination_sample/synthetic_sixinone_train.json",
        "/mnt/c/Projects/FG-PRM/data/hallucination_sample/synthetic_sixinone_dev.json"
    ]
    
    for file_path in data_files:
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
            print(f"   ✓ {Path(file_path).name} ({file_size:.1f} MB)")
        else:
            print(f"   ❌ {Path(file_path).name}")
            return False
    
    # Validate binary conversion logic
    print("\n3. Testing binary conversion logic...")
    
    ERROR_TYPES = {
        "Calculation-Error", "Context-Inconsistency", "Fabrication",
        "Factual-Inconsistency", "Instruction-Inconsistency", 
        "Logical-Inconsistency", "F-ERR"
    }
    
    def convert_to_binary(feedback_tags):
        return 1 if any(tag in ERROR_TYPES for tag in feedback_tags) else 0
    
    test_cases = [
        # No errors
        (["O", "O", "O"], 0),
        (["Ignore", "O", "Ignore"], 0),
        
        # Single error types
        (["O", "Calculation-Error", "O"], 1),
        (["Context-Inconsistency", "O"], 1),
        (["O", "Fabrication"], 1),
        (["Factual-Inconsistency"], 1),
        (["O", "Instruction-Inconsistency", "O"], 1),
        (["Logical-Inconsistency", "Ignore"], 1),
        (["F-ERR", "O"], 1),
        
        # Mixed scenarios
        (["O", "Calculation-Error", "Ignore", "Context-Inconsistency"], 1),
        (["Ignore", "O", "Ignore", "O"], 0),
    ]
    
    for tags, expected in test_cases:
        result = convert_to_binary(tags)
        if result == expected:
            print(f"   ✓ {tags} -> {result}")
        else:
            print(f"   ❌ {tags} -> {result} (expected {expected})")
            return False
    
    # Test with real data sample
    print("\n4. Testing with real sixinone data...")
    
    train_file = "/mnt/c/Projects/FG-PRM/data/hallucination_sample/synthetic_sixinone_train.json"
    
    try:
        sample_count = 0
        error_examples = 0
        no_error_examples = 0
        error_type_distribution = Counter()
        
        with open(train_file, 'r') as f:
            for line in f:
                if sample_count >= 100:  # Test first 100 examples
                    break
                
                try:
                    data = json.loads(line.strip())
                    feedback_tags = data.get('feedback_tags', [])
                    
                    # Convert to binary
                    binary_label = convert_to_binary(feedback_tags)
                    
                    if binary_label == 1:
                        error_examples += 1
                        # Track which error types are present
                        for tag in feedback_tags:
                            if tag in ERROR_TYPES:
                                error_type_distribution[tag] += 1
                    else:
                        no_error_examples += 1
                    
                    sample_count += 1
                    
                except Exception as e:
                    continue
        
        print(f"   ✓ Processed {sample_count} examples")
        print(f"   ✓ Binary distribution: {no_error_examples} no-error, {error_examples} has-error")
        print(f"   ✓ Error types found: {list(error_type_distribution.keys())}")
        
        if len(error_type_distribution) >= 5:  # Should find most error types
            print(f"   ✓ Comprehensive error coverage validated")
        else:
            print(f"   ⚠ Limited error type coverage: {len(error_type_distribution)} types")
        
    except Exception as e:
        print(f"   ❌ Error testing real data: {e}")
        return False
    
    # Check training script configuration
    print("\n5. Validating training configuration...")
    
    try:
        with open("rpe_train_fact_rm_modernbert.sh", 'r') as f:
            script_content = f.read()
            
        # Check for sixinone dataset usage
        if "synthetic_sixinone_train.json" in script_content:
            print("   ✓ Training script uses comprehensive sixinone dataset")
        else:
            print("   ❌ Training script not configured for sixinone dataset")
            return False
        
        # Check for appropriate parameters
        checks = [
            ("answerdotai/ModernBERT-large", "ModernBERT model specified"),
            ("--metric_for_best_model f1", "F1 metric for imbalanced data"),
            ("--gradient_accumulation_steps", "Gradient accumulation configured"),
            ("--max_seq_length 4096", "Appropriate sequence length"),
        ]
        
        for check_str, description in checks:
            if check_str in script_content:
                print(f"   ✓ {description}")
            else:
                print(f"   ⚠ {description} - check configuration")
        
    except Exception as e:
        print(f"   ❌ Error validating training script: {e}")
        return False
    
    print("\n" + "=" * 70)
    print("✅ COMPREHENSIVE PIPELINE VALIDATION PASSED")
    print("\nThe ModernBERT pipeline is ready for comprehensive multi-error training!")
    
    return True


def print_usage_instructions():
    """Print usage instructions for the validated pipeline."""
    print("\n📋 USAGE INSTRUCTIONS")
    print("-" * 30)
    print("To start comprehensive multi-error training:")
    print()
    print("1. Set GPU devices:")
    print("   export CUDA_VISIBLE_DEVICES=0,1,2,3")
    print()
    print("2. Navigate to source directory:")
    print("   cd /path/to/FG-PRM/src")
    print()
    print("3. Run training:")
    print("   bash reward_modeling/rpe_train_fact_rm_modernbert.sh")
    print()
    print("4. Monitor training:")
    print("   - Check wandb dashboard for metrics")
    print("   - Models saved to ../models/orm_modernbert_large_sixinone/")
    print()
    print("5. Evaluate results:")
    print("   python reward_modeling/eval_binary.py \\")
    print("     ../models/orm_modernbert_large_sixinone/predictions.txt \\")
    print("     ../data/hallucination_sample/synthetic_sixinone_dev.json")


if __name__ == "__main__":
    success = validate_comprehensive_pipeline()
    
    if success:
        print_usage_instructions()
    else:
        print("\n❌ Pipeline validation failed. Please check the errors above.")
        exit(1)
