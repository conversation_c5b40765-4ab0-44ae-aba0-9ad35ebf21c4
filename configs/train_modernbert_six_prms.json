{"model_name_or_path": "answerdotai/ModernBERT-base", "config_name": null, "tokenizer_name": null, "cache_dir": "./cache", "model_revision": "main", "use_auth_token": false, "train_file": "data/hallucination_sample/synthetic_train_combined.jsonl", "validation_file": "data/hallucination_sample/synthetic_val_combined.jsonl", "test_file": "data/hallucination_sample/synthetic_test_combined.jsonl", "input_column": "text", "label_column": "feedback_tags", "max_seq_length": 512, "overwrite_cache": false, "pad_to_max_length": false, "preprocessing_num_workers": 4, "prediction_output_filename": "predictions.txt", "output_dir": "../models/prm_modernbert", "overwrite_output_dir": true, "do_train": true, "do_eval": true, "do_predict": false, "evaluation_strategy": "steps", "eval_steps": 500, "per_device_train_batch_size": 8, "per_device_eval_batch_size": 16, "gradient_accumulation_steps": 4, "learning_rate": 2e-05, "weight_decay": 0.01, "adam_beta1": 0.9, "adam_beta2": 0.999, "adam_epsilon": 1e-08, "max_grad_norm": 1.0, "num_train_epochs": 3, "max_steps": -1, "lr_scheduler_type": "linear", "warmup_ratio": 0.1, "warmup_steps": 0, "logging_dir": "./logs", "logging_strategy": "steps", "logging_steps": 100, "save_strategy": "steps", "save_steps": 500, "save_total_limit": 3, "seed": 42, "fp16": true, "bf16": false, "local_rank": -1, "dataloader_drop_last": false, "dataloader_num_workers": 0, "load_best_model_at_end": true, "metric_for_best_model": "eval_accuracy", "greater_is_better": true, "report_to": ["wandb"], "run_name": "modernbert_six_prms", "error_type": "Calculation-Error", "output_base_dir": "../models/prm_modernbert", "positive_reward": 1.0, "negative_reward": -1.0, "sep_token": "[SEP]"}