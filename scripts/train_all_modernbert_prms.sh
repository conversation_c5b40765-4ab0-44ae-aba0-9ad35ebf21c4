#!/bin/bash

# Training script for all 6 ModernBERT Process Reward Models
# This script trains 6 separate binary PRMs, one for each error type

set -e

# Configuration
BASE_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
CONFIG_FILE="$BASE_DIR/configs/train_modernbert_six_prms.json"
TRAIN_SCRIPT="$BASE_DIR/src/fine-tuning/train_modernbert_six_prms.py"
OUTPUT_BASE_DIR="$BASE_DIR/models/prm_modernbert"
DATA_DIR="$BASE_DIR/data/hallucination_sample"

# Error types to train
ERROR_TYPES=(
    "Calculation-Error"
    "Fabrication"
    "Context-Inconsistency"
    "Factual-Inconsistency"
    "Instruction-Inconsistency"
    "Logical-Inconsistency"
)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}ModernBERT Fine-Grained PRM Training${NC}"
echo -e "${BLUE}========================================${NC}"

# Check if required files exist
if [ ! -f "$TRAIN_SCRIPT" ]; then
    echo -e "${RED}Error: Training script not found at $TRAIN_SCRIPT${NC}"
    exit 1
fi

if [ ! -f "$CONFIG_FILE" ]; then
    echo -e "${RED}Error: Config file not found at $CONFIG_FILE${NC}"
    exit 1
fi

# Create output directory
mkdir -p "$OUTPUT_BASE_DIR"

# Check for training data
echo -e "${YELLOW}Checking training data...${NC}"
TRAIN_FILES_FOUND=0
for error_type in "${ERROR_TYPES[@]}"; do
    train_file="$DATA_DIR/synthetic_${error_type}_train.json"
    if [ -f "$train_file" ]; then
        echo -e "${GREEN}✓ Found training data for $error_type${NC}"
        ((TRAIN_FILES_FOUND++))
    else
        echo -e "${RED}✗ Missing training data for $error_type at $train_file${NC}"
    fi
done

if [ $TRAIN_FILES_FOUND -eq 0 ]; then
    echo -e "${RED}Error: No training data found. Please ensure training data is available.${NC}"
    exit 1
fi

echo -e "${YELLOW}Found $TRAIN_FILES_FOUND training files out of ${#ERROR_TYPES[@]} error types.${NC}"

# Function to train a single PRM
train_single_prm() {
    local error_type=$1
    local model_output_dir="$OUTPUT_BASE_DIR/${error_type,,}"  # lowercase
    
    echo -e "${BLUE}Training PRM for: $error_type${NC}"
    echo -e "${YELLOW}Output directory: $model_output_dir${NC}"
    
    # Create temporary config for this error type
    local temp_config="/tmp/config_${error_type,,}.json"
    
    # Update config with specific error type and output directory
    python3 -c "
import json
import sys

with open('$CONFIG_FILE', 'r') as f:
    config = json.load(f)

config['error_type'] = '$error_type'
config['output_dir'] = '$model_output_dir'
config['logging_dir'] = '$model_output_dir/logs'
config['run_name'] = 'modernbert_${error_type,,}'

# Update data files for this specific error type
config['train_file'] = '$DATA_DIR/synthetic_${error_type}_train.json'
config['validation_file'] = '$DATA_DIR/synthetic_${error_type}_val.json'
config['test_file'] = '$DATA_DIR/synthetic_${error_type}_test.json'

with open('$temp_config', 'w') as f:
    json.dump(config, f, indent=2)
"
    
    # Run training
    if python3 "$TRAIN_SCRIPT" "$temp_config"; then
        echo -e "${GREEN}✓ Successfully trained PRM for $error_type${NC}"
        rm -f "$temp_config"
        return 0
    else
        echo -e "${RED}✗ Failed to train PRM for $error_type${NC}"
        rm -f "$temp_config"
        return 1
    fi
}

# Main training loop
echo -e "${BLUE}Starting training for all error types...${NC}"
SUCCESSFUL_TRAININGS=0
FAILED_TRAININGS=0

for error_type in "${ERROR_TYPES[@]}"; do
    echo -e "\n${YELLOW}========================================${NC}"
    echo -e "${YELLOW}Training: $error_type${NC}"
    echo -e "${YELLOW}========================================${NC}"
    
    if train_single_prm "$error_type"; then
        ((SUCCESSFUL_TRAININGS++))
    else
        ((FAILED_TRAININGS++))
    fi
done

# Summary
echo -e "\n${BLUE}========================================${NC}"
echo -e "${BLUE}Training Summary${NC}"
echo -e "${BLUE}========================================${NC}"
echo -e "${GREEN}Successful trainings: $SUCCESSFUL_TRAININGS${NC}"
echo -e "${RED}Failed trainings: $FAILED_TRAININGS${NC}"

if [ $SUCCESSFUL_TRAININGS -eq ${#ERROR_TYPES[@]} ]; then
    echo -e "${GREEN}🎉 All PRMs trained successfully!${NC}"
    echo -e "${YELLOW}Models saved in: $OUTPUT_BASE_DIR${NC}"
    exit 0
else
    echo -e "${RED}⚠️  Some trainings failed. Check logs for details.${NC}"
    exit 1
fi
