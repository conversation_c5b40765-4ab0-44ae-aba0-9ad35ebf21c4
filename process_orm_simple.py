#!/usr/bin/env python3
"""
Simple ORM dataset processor - processes files one by one to avoid memory issues.
"""

import json
import os
import random
from collections import defaultdict

def load_and_count_file(filepath):
    """Load file and return count."""
    if not os.path.exists(filepath):
        print(f"Warning: {filepath} not found")
        return []
    
    data = []
    with open(filepath, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line:
                try:
                    data.append(json.loads(line))
                except json.JSONDecodeError:
                    continue
    return data

def create_duplicate_key(item):
    """Create key for duplicate detection."""
    text_str = ''.join(item['text']) if isinstance(item['text'], list) else str(item['text'])
    tags_str = ''.join(item['feedback_tags']) if isinstance(item['feedback_tags'], list) else str(item['feedback_tags'])
    return text_str + '|||' + tags_str

def main():
    error_types = [
        "Fabrication", "Calculation-Error", "Context-Inconsistency",
        "Factual-Inconsistency", "Instruction-Inconsistency", "Logical-Inconsistency"
    ]
    
    base_dir = "data/hallucination_sample"
    output_dir = "data/hallucination_sample/orm_sample_cg-h"
    
    print("=== ORM Dataset Processing ===")
    
    # Step 1: Load all files and collect data
    all_data = []
    file_stats = {}
    
    for error_type in error_types:
        print(f"\nProcessing {error_type}...")
        
        # Train file
        train_file = os.path.join(base_dir, f"synthetic_{error_type}_orm_train.json")
        train_data = load_and_count_file(train_file)
        file_stats[f"{error_type}_train"] = len(train_data)
        all_data.extend(train_data)
        print(f"  Train: {len(train_data)} records")
        
        # Dev file  
        dev_file = os.path.join(base_dir, f"synthetic_{error_type}_orm_dev.json")
        dev_data = load_and_count_file(dev_file)
        file_stats[f"{error_type}_dev"] = len(dev_data)
        all_data.extend(dev_data)
        print(f"  Dev: {len(dev_data)} records")
        
        print(f"  Subtotal: {len(all_data)} records")
    
    print(f"\nTotal loaded: {len(all_data)} records")
    
    # Step 2: Remove duplicates
    print("\nRemoving duplicates...")
    seen_keys = set()
    unique_data = []
    duplicates = 0
    
    for i, item in enumerate(all_data):
        if i % 5000 == 0:
            print(f"  Processed {i}/{len(all_data)} records...")
        
        key = create_duplicate_key(item)
        if key not in seen_keys:
            seen_keys.add(key)
            unique_data.append(item)
        else:
            duplicates += 1
    
    print(f"Removed {duplicates} duplicates, kept {len(unique_data)} unique records")
    
    # Step 3: Get class distribution
    class_counts = defaultdict(int)
    for item in unique_data:
        tags = item['feedback_tags']
        if isinstance(tags, list):
            non_ignore = [t for t in tags if t != "Ignore"]
            if non_ignore:
                main_tag = max(set(non_ignore), key=non_ignore.count)
                class_counts[main_tag] += 1
            else:
                class_counts["No_Error"] += 1
        else:
            class_counts["Unknown"] += 1
    
    print(f"\nClass distribution: {dict(class_counts)}")
    
    # Step 4: Split data (80/10/10)
    print("\nSplitting data...")
    random.seed(42)
    random.shuffle(unique_data)
    
    total = len(unique_data)
    train_size = int(total * 0.8)
    val_size = int(total * 0.1)
    
    train_split = unique_data[:train_size]
    val_split = unique_data[train_size:train_size + val_size]
    test_split = unique_data[train_size + val_size:]
    
    print(f"Train: {len(train_split)}, Val: {len(val_split)}, Test: {len(test_split)}")
    
    # Step 5: Save files
    print("\nSaving files...")
    os.makedirs(output_dir, exist_ok=True)
    
    files_to_save = [
        (train_split, "synthetic_orm_sixinone_train.json"),
        (val_split, "synthetic_orm_sixinone_dev.json"),
        (test_split, "synthetic_orm_sixinone_test.json")
    ]
    
    for data, filename in files_to_save:
        filepath = os.path.join(output_dir, filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            for item in data:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')
        print(f"  Saved {filename}: {len(data)} records")
    
    # Summary
    print("\n=== SUMMARY ===")
    print(f"Input files processed: {len(file_stats)}")
    print(f"Total original records: {len(all_data)}")
    print(f"Duplicates removed: {duplicates}")
    print(f"Unique records: {len(unique_data)}")
    print(f"Final splits:")
    print(f"  Train (80%): {len(train_split)} records")
    print(f"  Validation (10%): {len(val_split)} records") 
    print(f"  Test (10%): {len(test_split)} records")
    print(f"\nOutput directory: {output_dir}")
    print("Processing completed successfully!")

if __name__ == "__main__":
    main()
