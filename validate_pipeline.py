#!/usr/bin/env python3
"""
Simple validation script for ModernBERT pipeline without external dependencies.
"""

import json
import os
from pathlib import Path

def validate_files():
    """Check that all required files exist."""
    print("Validating ModernBERT pipeline files...")
    
    required_files = [
        "rpe_train_fact_rm_modernbert.sh",
        "run_fg_rm_modernbert.py", 
        "eval_binary.py",
        "convert_to_binary.py",
        "README_ModernBERT.md"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
        else:
            print(f"  ✓ {file}")
    
    if missing_files:
        print(f"  ❌ Missing files: {missing_files}")
        return False
    
    print("  ✅ All required files present")
    return True

def validate_data_format():
    """Check data file format."""
    print("\nValidating data format...")
    
    data_dir = Path("../../data/hallucination_sample")
    if not data_dir.exists():
        print("  ⚠ Data directory not found")
        return True
    
    sample_files = list(data_dir.glob("*_orm_*.json"))
    if not sample_files:
        print("  ⚠ No ORM data files found")
        return True
    
    sample_file = sample_files[0]
    print(f"  Testing data format with: {sample_file.name}")
    
    try:
        with open(sample_file, 'r') as f:
            first_line = f.readline().strip()
            if first_line:
                data = json.loads(first_line)
                
                required_keys = ["text", "feedback_tags"]
                for key in required_keys:
                    if key not in data:
                        print(f"  ❌ Missing key '{key}' in data")
                        return False
                    print(f"  ✓ Found key '{key}'")
                
                # Check data types
                if not isinstance(data["text"], list):
                    print("  ❌ 'text' should be a list")
                    return False
                
                if not isinstance(data["feedback_tags"], list):
                    print("  ❌ 'feedback_tags' should be a list")
                    return False
                
                if len(data["text"]) != len(data["feedback_tags"]):
                    print("  ❌ 'text' and 'feedback_tags' should have same length")
                    return False
                
                print(f"  ✓ Data format valid ({len(data['text'])} tokens)")
                
    except Exception as e:
        print(f"  ❌ Error reading data file: {e}")
        return False
    
    print("  ✅ Data format validation passed")
    return True

def validate_binary_conversion():
    """Test binary conversion logic."""
    print("\nValidating binary conversion logic...")
    
    def convert_to_binary(feedback_tags):
        return 1 if any(tag not in ["O", "Ignore"] for tag in feedback_tags) else 0
    
    test_cases = [
        (["O", "O", "O"], 0),
        (["O", "F-ERR", "O"], 1),
        (["Ignore", "O"], 0),
        (["Calculation-Error"], 1),
        (["O", "Context-Inconsistency", "Ignore"], 1),
    ]
    
    for tags, expected in test_cases:
        result = convert_to_binary(tags)
        if result != expected:
            print(f"  ❌ Failed: {tags} -> {result} (expected {expected})")
            return False
        print(f"  ✓ {tags} -> {result}")
    
    print("  ✅ Binary conversion validation passed")
    return True

def main():
    print("ModernBERT Pipeline Validation")
    print("=" * 40)
    
    all_passed = True
    all_passed &= validate_files()
    all_passed &= validate_data_format()
    all_passed &= validate_binary_conversion()
    
    print("\n" + "=" * 40)
    if all_passed:
        print("✅ Pipeline validation PASSED")
        print("\nThe ModernBERT pipeline is ready to use!")
        print("To start training, run:")
        print("  cd /path/to/FG-PRM/src")
        print("  export CUDA_VISIBLE_DEVICES=0,1,2,3")
        print("  bash reward_modeling/rpe_train_fact_rm_modernbert.sh")
    else:
        print("❌ Pipeline validation FAILED")
        print("Please check the errors above and fix them.")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    exit(main())
