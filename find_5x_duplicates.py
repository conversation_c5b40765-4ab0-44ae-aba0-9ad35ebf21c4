#!/usr/bin/env python3
"""
<PERSON>ript to find and list specific line numbers for entries that appear exactly 5 times
in the training dataset.
"""

import json
import os
import hashlib
from collections import defaultdict, Counter

def create_content_hash(entry):
    """
    Create a hash of the content fields (text + feedback_tags) for duplicate detection.
    """
    text_str = str(entry.get('text', []))
    feedback_str = str(entry.get('feedback_tags', []))
    combined_content = text_str + "|" + feedback_str
    return hashlib.md5(combined_content.encode('utf-8')).hexdigest()

def find_5x_duplicates(file_path):
    """
    Find all content groups that appear exactly 5 times in the file.
    """
    if not os.path.exists(file_path):
        return {"error": f"File not found: {file_path}"}
    
    print(f"Analyzing file: {os.path.basename(file_path)}")
    print(f"Looking for content that appears exactly 5 times...")
    
    # Storage for analysis
    entries_by_hash = defaultdict(list)
    total_entries = 0
    
    # Read all entries and group by content hash
    with open(file_path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if not line:
                continue
                
            try:
                entry = json.loads(line)
                total_entries += 1
                
                # Create content hash
                content_hash = create_content_hash(entry)
                
                # Store detailed entry information
                entry_info = {
                    'line_num': line_num,
                    'id': entry.get('id', 'NO_ID'),
                    'text': entry.get('text', []),
                    'feedback_tags': entry.get('feedback_tags', []),
                    'text_preview': ' '.join(entry.get('text', [])[:10]) if entry.get('text') else '',
                    'text_length': len(entry.get('text', [])),
                    'feedback_tags_count': len(entry.get('feedback_tags', []))
                }
                
                entries_by_hash[content_hash].append(entry_info)
                
            except json.JSONDecodeError as e:
                print(f"Error parsing JSON at line {line_num}: {e}")
                continue
    
    # Find groups that appear exactly 5 times
    five_time_groups = []
    for content_hash, entries in entries_by_hash.items():
        if len(entries) == 5:
            five_time_groups.append({
                'content_hash': content_hash,
                'entries': entries,
                'text_preview': entries[0]['text_preview'],
                'text_length': entries[0]['text_length'],
                'feedback_tags_count': entries[0]['feedback_tags_count']
            })
    
    # Sort by first occurrence line number for consistent ordering
    five_time_groups.sort(key=lambda x: x['entries'][0]['line_num'])
    
    return {
        'file_path': file_path,
        'total_entries': total_entries,
        'five_time_groups': five_time_groups,
        'num_five_time_groups': len(five_time_groups)
    }

def print_5x_results(results):
    """Print detailed results for 5x duplicate groups."""
    
    if "error" in results:
        print(f"Error: {results['error']}")
        return
    
    print(f"\n=== Entries Appearing Exactly 5 Times ===")
    print(f"File: {os.path.basename(results['file_path'])}")
    print(f"Total entries in file: {results['total_entries']:,}")
    print(f"Number of content groups appearing exactly 5 times: {results['num_five_time_groups']}")
    print(f"Total entries in these groups: {results['num_five_time_groups'] * 5}")
    print(f"Extra duplicate entries: {results['num_five_time_groups'] * 4}")
    
    if results['num_five_time_groups'] == 0:
        print("\n❌ No content groups found that appear exactly 5 times.")
        return
    
    print(f"\n--- Detailed Breakdown ---")
    
    for i, group in enumerate(results['five_time_groups'], 1):
        print(f"\nGroup {i}:")
        print(f"  Content preview: \"{group['text_preview']}...\"")
        print(f"  Text length: {group['text_length']} tokens")
        print(f"  Feedback tags: {group['feedback_tags_count']} tags")
        print(f"  Content hash: {group['content_hash'][:16]}...")
        print(f"  Found at these 5 locations:")
        
        for j, entry in enumerate(group['entries'], 1):
            print(f"    {j}. Line {entry['line_num']:5d} | ID: {entry['id']}")
        
        # Show line numbers in a compact format
        line_numbers = [entry['line_num'] for entry in group['entries']]
        print(f"  Line numbers: {line_numbers}")

def main():
    """Main function to find 5x duplicates in training dataset."""
    
    file_path = "data/hallucination_sample/synthetic_AllErr_train.json"
    
    print("=== Finding 5x Duplicate Groups in Training Dataset ===")
    print("Searching for content that appears exactly 5 times...")
    
    # Analyze the file
    results = find_5x_duplicates(file_path)
    
    # Print detailed results
    print_5x_results(results)
    
    # Verification against expected count
    expected_groups = 17
    found_groups = results.get('num_five_time_groups', 0)
    
    print(f"\n=== Verification ===")
    print(f"Expected groups (from previous analysis): {expected_groups}")
    print(f"Found groups: {found_groups}")
    
    if found_groups == expected_groups:
        print("✅ Count matches previous analysis!")
    else:
        print(f"⚠️  Count mismatch - difference: {abs(found_groups - expected_groups)}")
    
    print(f"\n✅ Analysis completed!")

if __name__ == "__main__":
    main()
