# FG-PRM - Streamlined Training Pipeline

Repository for "Fine-grained Hallucination Mitigation and Detection in Language Model Reasoning" - **Streamlined Version**

This repository contains a focused, production-ready implementation with only the essential components for training Longformer and ModernBERT reward models for hallucination detection.

## 🚀 Quick Start

### Supported Models
- **Longformer**: Token-level classification for fine-grained reward modeling
- **ModernBERT**: Binary sequence classification for efficient reward modeling

### Training Pipelines
```bash
cd src

# Longformer training (multiple variants available)
bash reward_modeling/rpe_train_fact_rm_longformer.sh

# ModernBERT training (recommended for efficiency)
bash reward_modeling/rpe_train_fact_rm_modernbert.sh
```

## 📁 Project Structure

```
FG-PRM/
├── src/
│   ├── reward_modeling/          # Core training scripts
│   │   ├── rpe_train_fact_rm_longformer*.sh
│   │   ├── rpe_train_fact_rm_modernbert.sh
│   │   ├── run_fg_rm.py          # Longformer training script
│   │   ├── run_fg_rm_modernbert.py # ModernBERT training script
│   │   └── my_longformer.py      # Custom Longformer implementation
│   ├── verification/             # Model verification utilities
│   ├── analysis/                 # Results analysis tools
│   ├── utils/                    # Shared utilities
│   └── config.yml               # Configuration file
├── data/                        # Training datasets
│   └── hallucination_sample/   # Processed datasets
└── models/                      # Trained model outputs
```

## 🔧 Installation

### Prerequisites
- Python 3.8+
- PyTorch 2.0+
- CUDA-capable GPU(s)

### Dependencies
```bash
pip install torch transformers datasets evaluate scipy numpy wandb
```

## 📊 Datasets

### Available Datasets
- **ORM Datasets**: Binary classification data in `data/hallucination_sample/orm_sample_cg-h/`
- **PRM Datasets**: Token-level classification data in `data/hallucination_sample/prm_sample_fg-prm/`
- **Sixinone Datasets**: Combined multi-error datasets

### Dataset Structure
```json
{
  "id": "unique_identifier",
  "text": ["token1", "token2", "token3"],
  "feedback_tags": ["O", "O", "F-ERR"]
}
```

## 🏃‍♂️ Training

### Longformer Training
```bash
cd src

# Basic Longformer training
bash reward_modeling/rpe_train_fact_rm_longformer.sh

# Sixinone dataset training
bash reward_modeling/rpe_train_fact_rm_longformer_sixinone.sh

# Fine-grained ORM training
bash reward_modeling/rpe_train_fact_rm_longformer_fg-orm.sh
```

### ModernBERT Training
```bash
cd src

# ModernBERT binary classification
bash reward_modeling/rpe_train_fact_rm_modernbert.sh
```

For detailed ModernBERT training instructions, see [README_ModernBERT_Pipeline.md](README_ModernBERT_Pipeline.md).

## 🎯 Key Features

### Streamlined Codebase
- ✅ **Focused**: Only Longformer and ModernBERT pipelines
- ✅ **Clean**: Removed obsolete code and dependencies
- ✅ **Efficient**: Optimized training scripts and configurations
- ✅ **Documented**: Comprehensive documentation and examples

### Training Capabilities
- **Multi-GPU Support**: Distributed training with `torchrun`
- **Mixed Precision**: BF16 training for efficiency
- **Comprehensive Metrics**: Accuracy, F1, Precision, Recall
- **Experiment Tracking**: Weights & Biases integration
- **Early Stopping**: Based on validation metrics

### Error Type Support
- Fabrication
- Calculation-Error
- Context-Inconsistency
- Factual-Inconsistency
- Instruction-Inconsistency
- Logical-Inconsistency

## 📈 Performance

### ModernBERT Advantages
- **20x smaller** than Llama-3-8B (395M vs 8B parameters)
- **2-3x faster** inference
- **Better context** with bidirectional attention
- **Longer sequences** (up to 8192 tokens)

### Expected Results
- **Accuracy**: 80-90%
- **F1 Score**: 75-85%
- **Training Time**: 2-4 hours on 4x RTX 4090

## 🛠️ Configuration

### Main Configuration
Edit `src/config.yml` for dataset paths and model settings:

```yaml
# Supported models
longformer: allenai/longformer-base-4096
modernbert: answerdotai/ModernBERT-large
prm_model: longformer-4096_gsm8k
```

### Training Parameters
Key parameters in training scripts:
- `--num_train_epochs`: Training epochs (50 recommended)
- `--per_device_train_batch_size`: Batch size per GPU
- `--learning_rate`: Learning rate (1e-5 for ModernBERT)
- `--max_seq_length`: Maximum sequence length

## 🔍 Verification & Analysis

### Model Verification
```bash
cd src/verification
python verify_longformer.py
```

### Results Analysis
```bash
cd src/analysis
python evaluate_reasoning_steps.py
```

## 📝 What's Been Removed

This streamlined version has removed:
- ❌ Llama3 training pipelines and dependencies
- ❌ T5, GPT, and other model implementations
- ❌ Obsolete compact and experimental variants
- ❌ Unused API integrations (OpenAI, Anthropic)
- ❌ Legacy configuration files and scripts

## 🤝 Contributing

This is a focused, production-ready version. For research and experimental features, please refer to the original repository.

## 📄 License

Please refer to the original paper and repository for licensing information.

## 📚 Citation

If you use this code, please cite the original paper:
```
Fine-grained Hallucination Mitigation and Detection in Language Model Reasoning
```
