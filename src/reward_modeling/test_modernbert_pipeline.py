#!/usr/bin/env python3
"""
Test script to validate the ModernBERT binary classification pipeline.
This script tests data loading, conversion, and basic model functionality.
"""

import json
import os
import sys
import tempfile
from pathlib import Path

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from convert_to_binary import convert_token_to_binary_label
# from eval_binary import BinaryClassificationMetric, convert_token_labels_to_binary


def test_binary_conversion():
    """Test token-to-binary label conversion."""
    print("Testing binary label conversion...")
    
    # Test cases
    test_cases = [
        (["O", "O", "O"], 0),  # No error
        (["O", "F-ERR", "O"], 1),  # Has error
        (["Ignore", "O", "Ignore"], 0),  # Only ignore and O
        (["Calculation-Error", "O"], 1),  # Has specific error
        (["O", "Context-Inconsistency", "O", "Ignore"], 1),  # Mixed with error
    ]
    
    for feedback_tags, expected in test_cases:
        result = convert_token_to_binary_label(feedback_tags)
        assert result == expected, f"Failed for {feedback_tags}: got {result}, expected {expected}"
        print(f"  ✓ {feedback_tags} -> {result}")
    
    print("Binary conversion tests passed!\n")


def test_data_loading():
    """Test loading and processing actual data files."""
    print("Testing data loading...")
    
    # Find a sample data file
    data_dir = Path("../../data/hallucination_sample")
    sample_files = list(data_dir.glob("*_orm_train.json"))
    
    if not sample_files:
        print("  ⚠ No ORM training files found, skipping data loading test")
        return
    
    sample_file = sample_files[0]
    print(f"  Testing with: {sample_file}")
    
    # Load and process a few examples
    examples_processed = 0
    binary_labels = []
    
    with open(sample_file, 'r') as f:
        for line_num, line in enumerate(f):
            if examples_processed >= 5:  # Test first 5 examples
                break
                
            try:
                data = json.loads(line.strip())
                text_tokens = data.get("text", [])
                feedback_tags = data.get("feedback_tags", [])
                
                if text_tokens and feedback_tags:
                    binary_label = convert_token_to_binary_label(feedback_tags)
                    binary_labels.append(binary_label)
                    
                    print(f"  Example {line_num + 1}: {len(text_tokens)} tokens -> label {binary_label}")
                    examples_processed += 1
                    
            except Exception as e:
                print(f"  ⚠ Error processing line {line_num + 1}: {e}")
    
    if examples_processed > 0:
        label_counts = {0: binary_labels.count(0), 1: binary_labels.count(1)}
        print(f"  Processed {examples_processed} examples: {label_counts}")
        print("Data loading test passed!\n")
    else:
        print("  ⚠ No examples processed successfully\n")


def test_metrics():
    """Test binary classification metrics."""
    print("Testing binary classification metrics...")
    
    # Test data
    predictions = [0, 1, 0, 1, 1, 0, 1, 0]
    references = [0, 1, 1, 1, 0, 0, 1, 0]
    
    metric = BinaryClassificationMetric()
    results = metric.compute(predictions=predictions, references=references)
    
    # Check that all expected keys are present
    expected_keys = [
        'accuracy', 'precision_macro', 'recall_macro', 'f1_macro',
        'no_error_precision', 'no_error_recall', 'no_error_f1',
        'has_error_precision', 'has_error_recall', 'has_error_f1',
        'true_negatives', 'false_positives', 'false_negatives', 'true_positives'
    ]
    
    for key in expected_keys:
        assert key in results, f"Missing key: {key}"
    
    print(f"  ✓ Accuracy: {results['accuracy']:.3f}")
    print(f"  ✓ Macro F1: {results['f1_macro']:.3f}")
    print(f"  ✓ Confusion matrix: TN={results['true_negatives']}, FP={results['false_positives']}, FN={results['false_negatives']}, TP={results['true_positives']}")
    
    print("Metrics test passed!\n")


def test_model_loading():
    """Test ModernBERT model loading (requires internet connection)."""
    print("Testing ModernBERT model loading...")
    
    try:
        from transformers import AutoTokenizer, AutoModelForSequenceClassification
        
        model_name = "answerdotai/ModernBERT-large"
        
        # Test tokenizer loading
        print(f"  Loading tokenizer: {model_name}")
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        print(f"  ✓ Tokenizer loaded, vocab size: {len(tokenizer)}")
        
        # Test model loading
        print(f"  Loading model: {model_name}")
        model = AutoModelForSequenceClassification.from_pretrained(
            model_name, 
            num_labels=2,
            ignore_mismatched_sizes=True
        )
        print(f"  ✓ Model loaded, parameters: {sum(p.numel() for p in model.parameters()):,}")
        
        # Test tokenization
        test_text = "This is a test sentence for tokenization."
        tokens = tokenizer(test_text, return_tensors="pt", max_length=512, truncation=True, padding=True)
        print(f"  ✓ Tokenization test: {len(tokens['input_ids'][0])} tokens")
        
        # Test forward pass
        with torch.no_grad():
            outputs = model(**tokens)
            logits = outputs.logits
            print(f"  ✓ Forward pass test: output shape {logits.shape}")
        
        print("Model loading test passed!\n")
        
    except ImportError as e:
        print(f"  ⚠ Skipping model test due to missing dependencies: {e}\n")
    except Exception as e:
        print(f"  ⚠ Model loading test failed: {e}\n")


def test_data_conversion_script():
    """Test the data conversion script."""
    print("Testing data conversion script...")
    
    # Create sample data
    sample_data = [
        {
            "id": "test_1",
            "text": ["This", "is", "correct"],
            "feedback_tags": ["O", "O", "O"]
        },
        {
            "id": "test_2", 
            "text": ["This", "has", "error"],
            "feedback_tags": ["O", "F-ERR", "O"]
        }
    ]
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        for item in sample_data:
            f.write(json.dumps(item) + '\n')
        input_file = f.name
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        output_file = f.name
    
    try:
        # Test conversion
        from convert_to_binary import convert_dataset_to_binary
        convert_dataset_to_binary(input_file, output_file)
        
        # Verify output
        with open(output_file, 'r') as f:
            converted_data = [json.loads(line) for line in f]
        
        assert len(converted_data) == 2, f"Expected 2 items, got {len(converted_data)}"
        assert converted_data[0]['label'] == 0, "First item should have label 0"
        assert converted_data[1]['label'] == 1, "Second item should have label 1"
        
        print(f"  ✓ Converted {len(converted_data)} examples")
        print(f"  ✓ Labels: {[item['label'] for item in converted_data]}")
        
        print("Data conversion script test passed!\n")
        
    finally:
        # Clean up
        os.unlink(input_file)
        os.unlink(output_file)


def main():
    """Run all tests."""
    print("ModernBERT Pipeline Test Suite")
    print("=" * 50)
    
    try:
        test_binary_conversion()
        test_data_loading()
        test_metrics()
        test_data_conversion_script()
        
        # Only test model loading if torch is available
        try:
            import torch
            test_model_loading()
        except ImportError:
            print("⚠ PyTorch not available, skipping model loading test\n")
        
        print("✅ All tests passed! The ModernBERT pipeline is ready to use.")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
