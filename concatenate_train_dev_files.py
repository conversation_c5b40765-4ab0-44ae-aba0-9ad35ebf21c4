#!/usr/bin/env python3
"""
Script to concatenate 6 error-type files into single training and development datasets.
"""

import os
import json

def get_file_info(file_path):
    """Get file information including size and line count."""
    if not os.path.exists(file_path):
        return None
    
    size_bytes = os.path.getsize(file_path)
    line_count = 0
    
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            if line.strip():
                line_count += 1
    
    return {
        'size_bytes': size_bytes,
        'size_mb': size_bytes / (1024 * 1024),
        'line_count': line_count
    }

def concatenate_files(input_files, output_file):
    """Concatenate multiple JSON files into a single output file."""
    total_lines = 0
    files_processed = 0
    
    print(f"Creating: {output_file}")
    
    with open(output_file, 'w', encoding='utf-8') as outf:
        for input_file in input_files:
            if not os.path.exists(input_file):
                print(f"  ⚠️  File not found: {input_file}")
                continue
            
            file_info = get_file_info(input_file)
            print(f"  ✓ Adding: {os.path.basename(input_file)} ({file_info['line_count']:,} lines, {file_info['size_mb']:.1f} MB)")
            
            with open(input_file, 'r', encoding='utf-8') as inf:
                for line in inf:
                    line = line.strip()
                    if line:
                        outf.write(line + '\n')
                        total_lines += 1
            
            files_processed += 1
    
    # Get output file info
    output_info = get_file_info(output_file)
    print(f"  📄 Output: {total_lines:,} total lines, {output_info['size_mb']:.1f} MB")
    print(f"  ✅ Successfully concatenated {files_processed} files\n")
    
    return total_lines, files_processed

def main():
    """Main function to concatenate training and development files."""
    
    base_dir = "data/hallucination_sample"
    
    # Define the 6 error types
    error_types = [
        "Fabrication",
        "Calculation-Error", 
        "Context-Inconsistency",
        "Factual-Inconsistency",
        "Instruction-Inconsistency",
        "Logical-Inconsistency"
    ]
    
    print("=== Concatenating Error-Type Files ===\n")
    
    # Training files
    print("🔄 Processing Training Files:")
    train_files = []
    for error_type in error_types:
        train_file = os.path.join(base_dir, f"synthetic_{error_type}_train.json")
        train_files.append(train_file)
    
    train_output = os.path.join(base_dir, "synthetic_AllErr_train.json")
    train_total, train_processed = concatenate_files(train_files, train_output)
    
    # Development files
    print("🔄 Processing Development Files:")
    dev_files = []
    for error_type in error_types:
        # Try regular file first, then .ori file if regular doesn't exist
        dev_file = os.path.join(base_dir, f"synthetic_{error_type}_dev.json")
        dev_file_ori = os.path.join(base_dir, f"synthetic_{error_type}_dev.json.ori")
        
        if os.path.exists(dev_file):
            dev_files.append(dev_file)
        elif os.path.exists(dev_file_ori):
            dev_files.append(dev_file_ori)
            print(f"  ℹ️  Using .ori file for {error_type}")
        else:
            print(f"  ❌ Neither {dev_file} nor {dev_file_ori} found")
            dev_files.append(dev_file)  # Add anyway to show in error message
    
    dev_output = os.path.join(base_dir, "synthetic_AllErr_dev.json")
    dev_total, dev_processed = concatenate_files(dev_files, dev_output)
    
    # Summary
    print("=== Concatenation Summary ===")
    print(f"Training dataset:")
    print(f"  📁 Output file: {os.path.basename(train_output)}")
    print(f"  📊 Total entries: {train_total:,}")
    print(f"  🗂️  Files processed: {train_processed}/6")
    
    print(f"\nDevelopment dataset:")
    print(f"  📁 Output file: {os.path.basename(dev_output)}")
    print(f"  📊 Total entries: {dev_total:,}")
    print(f"  🗂️  Files processed: {dev_processed}/6")
    
    # Verify output files
    print(f"\n=== Output Verification ===")
    for output_file, dataset_name in [(train_output, "Training"), (dev_output, "Development")]:
        if os.path.exists(output_file):
            info = get_file_info(output_file)
            print(f"✅ {dataset_name}: {info['line_count']:,} lines, {info['size_mb']:.1f} MB")
        else:
            print(f"❌ {dataset_name}: File not created")
    
    print(f"\n🎉 Concatenation completed!")

if __name__ == "__main__":
    main()
