#!/usr/bin/env python3
"""
Deduplication script for ORM JSON files.
Removes duplicate entries based on the "id" field, keeping only the first occurrence.
"""

import json
import os
from collections import OrderedDict

def deduplicate_json_file(input_file, output_file):
    """
    Deduplicate a JSON file based on the 'id' field.
    
    Args:
        input_file (str): Path to input JSON file
        output_file (str): Path to output deduplicated JSON file
    
    Returns:
        tuple: (original_count, deduplicated_count, duplicates_removed)
    """
    seen_ids = set()
    unique_entries = []
    original_count = 0
    
    print(f"Processing {input_file}...")
    
    with open(input_file, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if not line:
                continue
                
            try:
                entry = json.loads(line)
                original_count += 1
                
                # Check if this ID has been seen before
                entry_id = entry.get('id')
                if entry_id is None:
                    print(f"Warning: Entry at line {line_num} has no 'id' field")
                    continue
                    
                if entry_id not in seen_ids:
                    seen_ids.add(entry_id)
                    unique_entries.append(entry)
                else:
                    print(f"Duplicate found: ID '{entry_id}' at line {line_num}")
                    
            except json.JSONDecodeError as e:
                print(f"Error parsing JSON at line {line_num}: {e}")
                continue
    
    # Write deduplicated entries to output file
    with open(output_file, 'w', encoding='utf-8') as f:
        for entry in unique_entries:
            json.dump(entry, f, ensure_ascii=False, separators=(',', ':'))
            f.write('\n')
    
    deduplicated_count = len(unique_entries)
    duplicates_removed = original_count - deduplicated_count
    
    return original_count, deduplicated_count, duplicates_removed

def get_file_size(file_path):
    """Get file size in bytes and return formatted string."""
    size_bytes = os.path.getsize(file_path)
    
    # Convert to appropriate unit
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes / (1024 * 1024):.1f} MB"
    else:
        return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"

def main():
    """Main function to deduplicate both training and development files."""
    
    # Define file paths
    base_dir = "data/hallucination_sample"
    
    files_to_process = [
        {
            'input': os.path.join(base_dir, "synthetic_AllErr_orm_train.json"),
            'output': os.path.join(base_dir, "synthetic_AllErr_orm_train_dedup.json"),
            'name': 'Training'
        },
        {
            'input': os.path.join(base_dir, "synthetic_AllErr_orm_dev.json"),
            'output': os.path.join(base_dir, "synthetic_AllErr_orm_dev_dedup.json"),
            'name': 'Development'
        }
    ]
    
    print("=== ORM File Deduplication ===\n")
    
    total_original = 0
    total_deduplicated = 0
    total_duplicates = 0
    
    for file_info in files_to_process:
        input_file = file_info['input']
        output_file = file_info['output']
        name = file_info['name']
        
        if not os.path.exists(input_file):
            print(f"Error: Input file {input_file} does not exist!")
            continue
        
        # Get original file size
        original_size = get_file_size(input_file)
        
        # Perform deduplication
        original_count, deduplicated_count, duplicates_removed = deduplicate_json_file(
            input_file, output_file
        )
        
        # Get deduplicated file size
        dedup_size = get_file_size(output_file)
        
        # Update totals
        total_original += original_count
        total_deduplicated += deduplicated_count
        total_duplicates += duplicates_removed
        
        # Print results for this file
        print(f"\n--- {name} Data Results ---")
        print(f"Input file:  {input_file}")
        print(f"Output file: {output_file}")
        print(f"Original entries: {original_count:,}")
        print(f"Deduplicated entries: {deduplicated_count:,}")
        print(f"Duplicates removed: {duplicates_removed:,}")
        print(f"Original file size: {original_size}")
        print(f"Deduplicated file size: {dedup_size}")
        
        if original_count > 0:
            reduction_percent = (duplicates_removed / original_count) * 100
            print(f"Reduction: {reduction_percent:.2f}%")
    
    # Print overall summary
    print(f"\n=== Overall Summary ===")
    print(f"Total original entries: {total_original:,}")
    print(f"Total deduplicated entries: {total_deduplicated:,}")
    print(f"Total duplicates removed: {total_duplicates:,}")
    
    if total_original > 0:
        overall_reduction = (total_duplicates / total_original) * 100
        print(f"Overall reduction: {overall_reduction:.2f}%")
    
    print("\nDeduplication completed successfully!")

if __name__ == "__main__":
    main()
