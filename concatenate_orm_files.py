#!/usr/bin/env python3
"""
Script to concatenate 6 ORM files into single training and development datasets.
"""

import json
import os
from pathlib import Path

def concatenate_orm_files():
    """Concatenate the 6 ORM files for both training and development datasets."""
    
    # Define the base directory
    base_dir = Path("data/hallucination_sample")
    
    # Define the error types
    error_types = [
        "Fabrication",
        "Calculation-Error", 
        "Context-Inconsistency",
        "Factual-Inconsistency",
        "Instruction-Inconsistency",
        "Logical-Inconsistency"
    ]
    
    # Process training files
    print("Processing training files...")
    train_files = [f"synthetic_{error_type}_orm_train.json" for error_type in error_types]
    concatenate_files(base_dir, train_files, "synthetic_AllErr_orm_train.json")
    
    # Process development files  
    print("Processing development files...")
    dev_files = [f"synthetic_{error_type}_orm_dev.json" for error_type in error_types]
    concatenate_files(base_dir, dev_files, "synthetic_AllErr_orm_dev.json")
    
    print("Concatenation completed successfully!")

def concatenate_files(base_dir, input_files, output_filename):
    """Concatenate multiple JSON files into a single output file."""
    
    output_path = base_dir / output_filename
    total_records = 0
    
    with open(output_path, 'w', encoding='utf-8') as outfile:
        for i, filename in enumerate(input_files):
            input_path = base_dir / filename
            
            if not input_path.exists():
                print(f"Warning: File {filename} not found, skipping...")
                continue
                
            print(f"  Processing {filename}...")
            
            with open(input_path, 'r', encoding='utf-8') as infile:
                file_records = 0
                for line in infile:
                    line = line.strip()
                    if line:  # Skip empty lines
                        # Validate JSON format
                        try:
                            json.loads(line)
                            outfile.write(line + '\n')
                            file_records += 1
                            total_records += 1
                        except json.JSONDecodeError as e:
                            print(f"    Warning: Invalid JSON in {filename}, line skipped: {e}")
                            
                print(f"    Added {file_records} records from {filename}")
    
    print(f"  Total records written to {output_filename}: {total_records}")

if __name__ == "__main__":
    concatenate_orm_files()
