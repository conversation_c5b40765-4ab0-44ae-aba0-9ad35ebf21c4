#!/usr/bin/env python3
"""
Process synthetic hallucination detection datasets.

This script:
1. Merges train and dev files for each error type
2. Removes exact duplicates (matching both text and feedback_tags)
3. Splits data into 80/10/10 train/validation/test with stratified sampling
4. Saves processed files with "clear" in the filename
"""

import json
import os
import random
from collections import defaultdict
from typing import List, Dict, Tuple, Any

def load_json_file(filepath: str) -> List[Dict]:
    """Load JSON file with one JSON object per line."""
    data = []
    if not os.path.exists(filepath):
        print(f"Warning: File {filepath} not found")
        return data
    
    with open(filepath, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line:
                try:
                    data.append(json.loads(line))
                except json.JSONDecodeError as e:
                    print(f"Error parsing JSON in {filepath}: {e}")
                    continue
    return data

def save_json_file(data: List[Dict], filepath: str):
    """Save data as JSON file with one object per line."""
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    with open(filepath, 'w', encoding='utf-8') as f:
        for item in data:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')

def create_duplicate_key(item: Dict) -> str:
    """Create a key for duplicate detection based on text and feedback_tags."""
    text_str = ''.join(item['text']) if isinstance(item['text'], list) else str(item['text'])
    tags_str = ''.join(item['feedback_tags']) if isinstance(item['feedback_tags'], list) else str(item['feedback_tags'])
    return text_str + '|||' + tags_str

def remove_duplicates(data: List[Dict]) -> List[Dict]:
    """Remove exact duplicates where both text and feedback_tags match 100%."""
    seen_keys = set()
    unique_data = []
    duplicates_count = 0
    
    for item in data:
        key = create_duplicate_key(item)
        if key not in seen_keys:
            seen_keys.add(key)
            unique_data.append(item)
        else:
            duplicates_count += 1
    
    print(f"  Removed {duplicates_count} duplicates, kept {len(unique_data)} unique records")
    return unique_data

def get_class_distribution(data: List[Dict]) -> Dict[str, int]:
    """Get distribution of classes based on feedback_tags."""
    class_counts = defaultdict(int)
    
    for item in data:
        feedback_tags = item['feedback_tags']
        if isinstance(feedback_tags, list):
            # Count non-"Ignore" tags
            non_ignore_tags = [tag for tag in feedback_tags if tag != "Ignore"]
            if non_ignore_tags:
                # Use the most frequent non-ignore tag as the class
                tag_counts = defaultdict(int)
                for tag in non_ignore_tags:
                    tag_counts[tag] += 1
                main_class = max(tag_counts.items(), key=lambda x: x[1])[0]
                class_counts[main_class] += 1
            else:
                class_counts["No_Error"] += 1
        else:
            class_counts["Unknown"] += 1
    
    return dict(class_counts)

def create_stratification_labels(data: List[Dict]) -> List[str]:
    """Create labels for stratified sampling."""
    labels = []
    
    for item in data:
        feedback_tags = item['feedback_tags']
        if isinstance(feedback_tags, list):
            # Count non-"Ignore" tags
            non_ignore_tags = [tag for tag in feedback_tags if tag != "Ignore"]
            if non_ignore_tags:
                # Use the most frequent non-ignore tag as the class
                tag_counts = defaultdict(int)
                for tag in non_ignore_tags:
                    tag_counts[tag] += 1
                main_class = max(tag_counts.items(), key=lambda x: x[1])[0]
                labels.append(main_class)
            else:
                labels.append("No_Error")
        else:
            labels.append("Unknown")
    
    return labels

def stratified_split(data: List[Dict], test_size: float = 0.2, random_state: int = 42) -> Tuple[List[Dict], List[Dict]]:
    """Perform stratified split maintaining class distribution."""
    if len(data) < 10:  # Too few samples for stratification
        split_idx = int(len(data) * (1 - test_size))
        return data[:split_idx], data[split_idx:]

    labels = create_stratification_labels(data)

    # Group data by labels for stratified sampling
    label_groups = defaultdict(list)
    for i, label in enumerate(labels):
        label_groups[label].append(i)

    # Perform stratified split manually
    random.seed(random_state)
    train_indices = []
    test_indices = []

    for label, indices in label_groups.items():
        random.shuffle(indices)
        split_point = int(len(indices) * (1 - test_size))
        train_indices.extend(indices[:split_point])
        test_indices.extend(indices[split_point:])

    # Shuffle the final indices
    random.shuffle(train_indices)
    random.shuffle(test_indices)

    train_data = [data[i] for i in train_indices]
    test_data = [data[i] for i in test_indices]

    return train_data, test_data

def process_error_type(error_type: str, base_dir: str, output_dir: str):
    """Process a single error type dataset."""
    print(f"\nProcessing {error_type}...")
    
    # Load train and dev files
    train_file = os.path.join(base_dir, f"synthetic_{error_type}_train.json")
    dev_file = os.path.join(base_dir, f"synthetic_{error_type}_dev.json")
    
    train_data = load_json_file(train_file)
    dev_data = load_json_file(dev_file)
    
    print(f"  Loaded {len(train_data)} train records and {len(dev_data)} dev records")
    
    # Merge datasets
    merged_data = train_data + dev_data
    print(f"  Merged total: {len(merged_data)} records")
    
    # Remove duplicates
    unique_data = remove_duplicates(merged_data)
    
    # Show class distribution
    class_dist = get_class_distribution(unique_data)
    print(f"  Class distribution: {class_dist}")
    
    # Split into train/val/test (80/10/10)
    # First split: 80% train, 20% temp (which will be split into 10% val, 10% test)
    train_split, temp_split = stratified_split(unique_data, test_size=0.2, random_state=42)
    
    # Second split: split the 20% temp into 10% val and 10% test (50/50 split of temp)
    val_split, test_split = stratified_split(temp_split, test_size=0.5, random_state=42)
    
    print(f"  Final splits - Train: {len(train_split)}, Val: {len(val_split)}, Test: {len(test_split)}")
    
    # Save processed files with "clear" in filename
    train_output = os.path.join(output_dir, f"synthetic_clear_{error_type}_train.json")
    val_output = os.path.join(output_dir, f"synthetic_clear_{error_type}_dev.json")
    test_output = os.path.join(output_dir, f"synthetic_clear_{error_type}_test.json")
    
    save_json_file(train_split, train_output)
    save_json_file(val_split, val_output)
    save_json_file(test_split, test_output)
    
    print(f"  Saved to: {train_output}, {val_output}, {test_output}")

def main():
    """Main processing function."""
    # Define error types to process
    error_types = [
        "Fabrication",
        "Calculation-Error", 
        "Context-Inconsistency",
        "Factual-Inconsistency",
        "Instruction-Inconsistency",
        "Logical-Inconsistency"
    ]
    
    base_dir = "data/hallucination_sample"
    output_dir = "data/hallucination_sample/prm_sample_fg-prm"
    
    print("Starting synthetic dataset processing...")
    print(f"Input directory: {base_dir}")
    print(f"Output directory: {output_dir}")
    
    # Process each error type
    for error_type in error_types:
        try:
            process_error_type(error_type, base_dir, output_dir)
        except Exception as e:
            print(f"Error processing {error_type}: {e}")
            continue
    
    print("\nProcessing completed!")
    
    # Summary
    print("\nSummary of output files:")
    for error_type in error_types:
        for split in ["train", "dev", "test"]:
            filename = f"synthetic_clear_{error_type}_{split}.json"
            filepath = os.path.join(output_dir, filename)
            if os.path.exists(filepath):
                with open(filepath, 'r') as f:
                    count = sum(1 for line in f if line.strip())
                print(f"  {filename}: {count} records")

if __name__ == "__main__":
    main()
