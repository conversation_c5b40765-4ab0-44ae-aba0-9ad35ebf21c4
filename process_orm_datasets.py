#!/usr/bin/env python3
"""
Process synthetic ORM (Outcome Reward Model) hallucination detection datasets.

This script:
1. Merges all 12 ORM files (6 train + 6 dev) into a single combined dataset
2. Removes exact duplicates (matching both text and feedback_tags)
3. Splits data into 80/10/10 train/validation/test with stratified sampling
4. Saves processed files as sixinone format for ORM training
"""

import json
import os
import random
from collections import defaultdict
from typing import List, Dict, Tuple, Any

def load_json_file(filepath: str) -> List[Dict]:
    """Load JSON file with one JSON object per line."""
    data = []
    if not os.path.exists(filepath):
        print(f"Warning: File {filepath} not found")
        return data
    
    with open(filepath, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line:
                try:
                    data.append(json.loads(line))
                except json.JSONDecodeError as e:
                    print(f"Error parsing JSON in {filepath}: {e}")
                    continue
    return data

def save_json_file(data: List[Dict], filepath: str):
    """Save data as JSON file with one object per line."""
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    with open(filepath, 'w', encoding='utf-8') as f:
        for item in data:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')

def create_duplicate_key(item: Dict) -> str:
    """Create a key for duplicate detection based on text and feedback_tags."""
    text_str = ''.join(item['text']) if isinstance(item['text'], list) else str(item['text'])
    tags_str = ''.join(item['feedback_tags']) if isinstance(item['feedback_tags'], list) else str(item['feedback_tags'])
    return text_str + '|||' + tags_str

def remove_duplicates(data: List[Dict]) -> List[Dict]:
    """Remove exact duplicates where both text and feedback_tags match 100%."""
    seen_keys = set()
    unique_data = []
    duplicates_count = 0

    print(f"  Processing {len(data)} records for deduplication...")

    for i, item in enumerate(data):
        if i % 1000 == 0 and i > 0:
            print(f"    Processed {i} records, found {duplicates_count} duplicates so far")

        key = create_duplicate_key(item)
        if key not in seen_keys:
            seen_keys.add(key)
            unique_data.append(item)
        else:
            duplicates_count += 1

    print(f"  Removed {duplicates_count} duplicates, kept {len(unique_data)} unique records")
    return unique_data

def get_class_distribution(data: List[Dict]) -> Dict[str, int]:
    """Get distribution of classes based on feedback_tags."""
    class_counts = defaultdict(int)
    
    for item in data:
        feedback_tags = item['feedback_tags']
        if isinstance(feedback_tags, list):
            # Count non-"Ignore" tags
            non_ignore_tags = [tag for tag in feedback_tags if tag != "Ignore"]
            if non_ignore_tags:
                # Use the most frequent non-ignore tag as the class
                tag_counts = defaultdict(int)
                for tag in non_ignore_tags:
                    tag_counts[tag] += 1
                main_class = max(tag_counts.items(), key=lambda x: x[1])[0]
                class_counts[main_class] += 1
            else:
                class_counts["No_Error"] += 1
        else:
            class_counts["Unknown"] += 1
    
    return dict(class_counts)

def create_stratification_labels(data: List[Dict]) -> List[str]:
    """Create labels for stratified sampling."""
    labels = []
    
    for item in data:
        feedback_tags = item['feedback_tags']
        if isinstance(feedback_tags, list):
            # Count non-"Ignore" tags
            non_ignore_tags = [tag for tag in feedback_tags if tag != "Ignore"]
            if non_ignore_tags:
                # Use the most frequent non-ignore tag as the class
                tag_counts = defaultdict(int)
                for tag in non_ignore_tags:
                    tag_counts[tag] += 1
                main_class = max(tag_counts.items(), key=lambda x: x[1])[0]
                labels.append(main_class)
            else:
                labels.append("No_Error")
        else:
            labels.append("Unknown")
    
    return labels

def stratified_split(data: List[Dict], test_size: float = 0.2, random_state: int = 42) -> Tuple[List[Dict], List[Dict]]:
    """Perform stratified split maintaining class distribution."""
    if len(data) < 10:  # Too few samples for stratification
        split_idx = int(len(data) * (1 - test_size))
        return data[:split_idx], data[split_idx:]
    
    labels = create_stratification_labels(data)
    
    # Group data by labels for stratified sampling
    label_groups = defaultdict(list)
    for i, label in enumerate(labels):
        label_groups[label].append(i)
    
    # Perform stratified split manually
    random.seed(random_state)
    train_indices = []
    test_indices = []
    
    for label, indices in label_groups.items():
        random.shuffle(indices)
        split_point = int(len(indices) * (1 - test_size))
        train_indices.extend(indices[:split_point])
        test_indices.extend(indices[split_point:])
    
    # Shuffle the final indices
    random.shuffle(train_indices)
    random.shuffle(test_indices)
    
    train_data = [data[i] for i in train_indices]
    test_data = [data[i] for i in test_indices]
    
    return train_data, test_data

def main():
    """Main processing function."""
    # Define error types and file patterns
    error_types = [
        "Fabrication",
        "Calculation-Error", 
        "Context-Inconsistency",
        "Factual-Inconsistency",
        "Instruction-Inconsistency",
        "Logical-Inconsistency"
    ]
    
    base_dir = "data/hallucination_sample"
    output_dir = "data/hallucination_sample/orm_sample_cg-h"
    
    print("Starting ORM dataset processing...")
    print(f"Input directory: {base_dir}")
    print(f"Output directory: {output_dir}")
    
    # Load all ORM files
    all_data = []
    total_loaded = 0

    print("\nLoading all ORM files...")
    for error_type in error_types:
        print(f"Processing {error_type}...")

        # Load train file
        train_file = os.path.join(base_dir, f"synthetic_{error_type}_orm_train.json")
        train_data = load_json_file(train_file)
        print(f"  {error_type} train: {len(train_data)} records")
        all_data.extend(train_data)
        total_loaded += len(train_data)

        # Load dev file
        dev_file = os.path.join(base_dir, f"synthetic_{error_type}_orm_dev.json")
        dev_data = load_json_file(dev_file)
        print(f"  {error_type} dev: {len(dev_data)} records")
        all_data.extend(dev_data)
        total_loaded += len(dev_data)

        # Print progress
        print(f"  Running total: {len(all_data)} records")
    
    print(f"\nTotal loaded: {total_loaded} records from all files")
    
    # Remove duplicates
    print("\nRemoving duplicates...")
    unique_data = remove_duplicates(all_data)
    
    # Show class distribution
    class_dist = get_class_distribution(unique_data)
    print(f"\nClass distribution: {class_dist}")
    
    # Split into train/val/test (80/10/10)
    print("\nSplitting data...")
    # First split: 80% train, 20% temp (which will be split into 10% val, 10% test)
    train_split, temp_split = stratified_split(unique_data, test_size=0.2, random_state=42)
    
    # Second split: split the 20% temp into 10% val and 10% test (50/50 split of temp)
    val_split, test_split = stratified_split(temp_split, test_size=0.5, random_state=42)
    
    print(f"Final splits - Train: {len(train_split)}, Val: {len(val_split)}, Test: {len(test_split)}")
    
    # Save processed files with sixinone naming convention
    train_output = os.path.join(output_dir, "synthetic_orm_sixinone_train.json")
    val_output = os.path.join(output_dir, "synthetic_orm_sixinone_dev.json")
    test_output = os.path.join(output_dir, "synthetic_orm_sixinone_test.json")
    
    save_json_file(train_split, train_output)
    save_json_file(val_split, val_output)
    save_json_file(test_split, test_output)
    
    print(f"\nSaved to:")
    print(f"  {train_output}")
    print(f"  {val_output}")
    print(f"  {test_output}")
    
    print("\nProcessing completed!")
    
    # Summary
    print("\nSummary of output files:")
    for filename, split_data in [
        ("synthetic_orm_sixinone_train.json", train_split),
        ("synthetic_orm_sixinone_dev.json", val_split),
        ("synthetic_orm_sixinone_test.json", test_split)
    ]:
        filepath = os.path.join(output_dir, filename)
        if os.path.exists(filepath):
            print(f"  {filename}: {len(split_data)} records")
    
    # Show final statistics
    print(f"\nFinal Statistics:")
    print(f"  Original total records: {total_loaded}")
    print(f"  Duplicates removed: {total_loaded - len(unique_data)}")
    print(f"  Unique records: {len(unique_data)}")
    print(f"  Train split (80%): {len(train_split)} records")
    print(f"  Validation split (10%): {len(val_split)} records")
    print(f"  Test split (10%): {len(test_split)} records")

if __name__ == "__main__":
    main()
