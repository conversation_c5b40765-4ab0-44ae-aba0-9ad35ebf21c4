#!/bin/bash

# Verification script for ModernBERT Fine-Grained Process Reward Models
# This script runs verification using the trained 6-model ModernBERT system

set -e

# Configuration
BASE_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
VERIFY_SCRIPT="$BASE_DIR/src/verification/verify_modernbert.py"
MODEL_BASE_DIR="$BASE_DIR/models/prm_modernbert"
TEST_DATA_DIR="$BASE_DIR/data/test"
RESULTS_DIR="$BASE_DIR/results/modernbert_verification"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}ModernBERT PRM Verification${NC}"
echo -e "${BLUE}========================================${NC}"

# Check if verification script exists
if [ ! -f "$VERIFY_SCRIPT" ]; then
    echo -e "${RED}Error: Verification script not found at $VERIFY_SCRIPT${NC}"
    exit 1
fi

# Check if models exist
if [ ! -d "$MODEL_BASE_DIR" ]; then
    echo -e "${RED}Error: Model directory not found at $MODEL_BASE_DIR${NC}"
    echo -e "${YELLOW}Please train the models first using train_all_modernbert_prms.sh${NC}"
    exit 1
fi

# Create results directory
mkdir -p "$RESULTS_DIR"

# Function to verify models exist
check_models() {
    echo -e "${YELLOW}Checking trained models...${NC}"
    
    local error_types=("calculation_error" "fabrication" "context_inconsistency" 
                      "factual_inconsistency" "instruction_inconsistency" "logical_inconsistency")
    local models_found=0
    
    for error_type in "${error_types[@]}"; do
        local model_dir="$MODEL_BASE_DIR/$error_type"
        if [ -d "$model_dir" ] && [ -f "$model_dir/config.json" ]; then
            echo -e "${GREEN}✓ Found model for $error_type${NC}"
            ((models_found++))
        else
            echo -e "${RED}✗ Missing model for $error_type${NC}"
        fi
    done
    
    if [ $models_found -eq 0 ]; then
        echo -e "${RED}Error: No trained models found. Please train models first.${NC}"
        exit 1
    elif [ $models_found -lt 6 ]; then
        echo -e "${YELLOW}Warning: Only $models_found out of 6 models found. Verification may be incomplete.${NC}"
    else
        echo -e "${GREEN}All 6 models found!${NC}"
    fi
}

# Function to run verification on a test file
run_verification() {
    local test_file=$1
    local output_name=$2
    
    echo -e "${BLUE}Running verification on: $(basename $test_file)${NC}"
    
    local output_file="$RESULTS_DIR/${output_name}_results.json"
    local log_file="$RESULTS_DIR/${output_name}_log.txt"
    
    if python3 "$VERIFY_SCRIPT" \
        --model_base_dir "$MODEL_BASE_DIR" \
        --test_file "$test_file" \
        --output_file "$output_file" \
        --device "cuda" \
        --verbose > "$log_file" 2>&1; then
        
        echo -e "${GREEN}✓ Verification completed successfully${NC}"
        echo -e "${YELLOW}Results saved to: $output_file${NC}"
        echo -e "${YELLOW}Log saved to: $log_file${NC}"
        
        # Extract and display key metrics
        if [ -f "$output_file" ]; then
            local accuracy=$(python3 -c "import json; data=json.load(open('$output_file')); print(f\"{data['verification_accuracy']:.4f}\")")
            local correct=$(python3 -c "import json; data=json.load(open('$output_file')); print(data['correct_selections'])")
            local total=$(python3 -c "import json; data=json.load(open('$output_file')); print(data['total_questions'])")
            
            echo -e "${GREEN}Accuracy: $accuracy ($correct/$total)${NC}"
        fi
        
        return 0
    else
        echo -e "${RED}✗ Verification failed${NC}"
        echo -e "${YELLOW}Check log file: $log_file${NC}"
        return 1
    fi
}

# Main execution
echo -e "${YELLOW}Starting ModernBERT verification process...${NC}"

# Check models
check_models

# Find test files
echo -e "${YELLOW}Looking for test files...${NC}"

TEST_FILES=()
if [ -d "$TEST_DATA_DIR" ]; then
    while IFS= read -r -d '' file; do
        TEST_FILES+=("$file")
    done < <(find "$TEST_DATA_DIR" -name "*.json" -o -name "*.jsonl" -print0)
fi

# If no test files found in test directory, look for common test file names
if [ ${#TEST_FILES[@]} -eq 0 ]; then
    echo -e "${YELLOW}No test files found in $TEST_DATA_DIR, checking for common test files...${NC}"
    
    COMMON_TEST_FILES=(
        "$BASE_DIR/data/test_data.json"
        "$BASE_DIR/data/test.json"
        "$BASE_DIR/data/validation.json"
        "$BASE_DIR/data/hallucination_sample/test.json"
    )
    
    for test_file in "${COMMON_TEST_FILES[@]}"; do
        if [ -f "$test_file" ]; then
            TEST_FILES+=("$test_file")
        fi
    done
fi

if [ ${#TEST_FILES[@]} -eq 0 ]; then
    echo -e "${RED}Error: No test files found.${NC}"
    echo -e "${YELLOW}Please provide test data in one of these locations:${NC}"
    echo -e "${YELLOW}  - $TEST_DATA_DIR/${NC}"
    echo -e "${YELLOW}  - $BASE_DIR/data/test_data.json${NC}"
    echo -e "${YELLOW}  - $BASE_DIR/data/test.json${NC}"
    exit 1
fi

echo -e "${GREEN}Found ${#TEST_FILES[@]} test file(s)${NC}"

# Run verification on all test files
SUCCESSFUL_VERIFICATIONS=0
FAILED_VERIFICATIONS=0

for test_file in "${TEST_FILES[@]}"; do
    echo -e "\n${YELLOW}========================================${NC}"
    echo -e "${YELLOW}Processing: $(basename $test_file)${NC}"
    echo -e "${YELLOW}========================================${NC}"
    
    # Generate output name from filename
    output_name=$(basename "$test_file" | sed 's/\.[^.]*$//')
    
    if run_verification "$test_file" "$output_name"; then
        ((SUCCESSFUL_VERIFICATIONS++))
    else
        ((FAILED_VERIFICATIONS++))
    fi
done

# Summary
echo -e "\n${BLUE}========================================${NC}"
echo -e "${BLUE}Verification Summary${NC}"
echo -e "${BLUE}========================================${NC}"
echo -e "${GREEN}Successful verifications: $SUCCESSFUL_VERIFICATIONS${NC}"
echo -e "${RED}Failed verifications: $FAILED_VERIFICATIONS${NC}"
echo -e "${YELLOW}Results directory: $RESULTS_DIR${NC}"

if [ $SUCCESSFUL_VERIFICATIONS -gt 0 ]; then
    echo -e "${GREEN}🎉 Verification completed!${NC}"
    echo -e "${YELLOW}Check the results directory for detailed metrics.${NC}"
    exit 0
else
    echo -e "${RED}⚠️  All verifications failed. Check logs for details.${NC}"
    exit 1
fi
