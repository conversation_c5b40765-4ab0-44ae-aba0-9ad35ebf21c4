# ModernBERT Fine-Grained Process Reward Models (FG-PRM) Training Guide

This guide provides comprehensive instructions for training 6 separate Process Reward Models (PRMs) using ModernBERT, replacing the original Longformer-based system.

## Overview

The FG-PRM system now uses ModernBERT as the base model and trains 6 separate binary PRMs, one for each error type:

1. **Calculation Error** - Detects mathematical calculation mistakes
2. **Fabrication** - Identifies made-up or false information
3. **Context Inconsistency** - Finds inconsistencies with given context
4. **Factual Inconsistency** - Detects factual errors
5. **Instruction Inconsistency** - Identifies deviations from instructions
6. **Logical Inconsistency** - Finds logical reasoning errors

## Architecture Changes

### From Longformer to ModernBERT

- **Base Model**: Complete replacement of Longformer with ModernBERT (`answerdotai/ModernBERT-base`)
- **Architecture**: 6 separate binary classifiers instead of single multi-class model
- **Input Format**: `question + y1 [SEP] y2 [SEP] ...` where labels are on [SEP] tokens
- **Loss Function**: Step-wise PRM loss for each binary classifier
- **Inference**: Sum of 6 PRM scores across steps during verification

## File Structure

```
src/fine-tuning/
├── my_modernbert.py                    # ModernBERT model implementations
├── train_modernbert_six_prms.py       # Main training script for 6 PRMs
├── reward_modernbert.py                # ModernBERT reward system integration
└── ...

src/verification/
├── verify_modernbert.py               # ModernBERT verification script
└── ...

configs/
├── train_modernbert_six_prms.json     # Training configuration
└── ...

scripts/
├── train_all_modernbert_prms.sh       # Training launcher script
└── ...

models/prm_modernbert/                  # Output directory for trained models
├── calculation_error/
├── fabrication/
├── context_inconsistency/
├── factual_inconsistency/
├── instruction_inconsistency/
└── logical_inconsistency/
```

## Training Process

### 1. Data Preparation

Ensure your training data is in the correct format:

```json
{
  "text": ["token1", "token2", "[SEP]", "token3", ...],
  "feedback_tags": ["O", "O", "Ignore", "ERR", ...]
}
```

Expected data files:
- `data/hallucination_sample/synthetic_Calculation-Error_train.json`
- `data/hallucination_sample/synthetic_Fabrication_train.json`
- `data/hallucination_sample/synthetic_Context-Inconsistency_train.json`
- `data/hallucination_sample/synthetic_Factual-Inconsistency_train.json`
- `data/hallucination_sample/synthetic_Instruction-Inconsistency_train.json`
- `data/hallucination_sample/synthetic_Logical-Inconsistency_train.json`

### 2. Configuration

Edit `configs/train_modernbert_six_prms.json` to adjust training parameters:

```json
{
  "model_name_or_path": "answerdotai/ModernBERT-base",
  "max_seq_length": 512,
  "per_device_train_batch_size": 8,
  "learning_rate": 2e-5,
  "num_train_epochs": 3,
  "output_base_dir": "../models/prm_modernbert"
}
```

### 3. Training Execution

#### Option A: Train All Models (Recommended)

```bash
# Make script executable
chmod +x scripts/train_all_modernbert_prms.sh

# Run training for all 6 error types
./scripts/train_all_modernbert_prms.sh
```

#### Option B: Train Individual Models

```bash
# Train a specific error type
python src/fine-tuning/train_modernbert_six_prms.py configs/train_modernbert_six_prms.json
```

### 4. Training Monitoring

- **Logs**: Check `models/prm_modernbert/{error_type}/logs/` for training logs
- **Wandb**: Monitor training progress if wandb is configured
- **Checkpoints**: Models saved in `models/prm_modernbert/{error_type}/`

## Model Usage

### Loading Trained Models

```python
from src.fine_tuning.my_modernbert import ModernBertForTokenClassification
from transformers import AutoTokenizer

# Load a specific PRM
model_path = "models/prm_modernbert/calculation_error"
model = ModernBertForTokenClassification.from_pretrained(model_path)
tokenizer = AutoTokenizer.from_pretrained(model_path)
```

### Using the Reward System

```python
from src.fine_tuning.reward_modernbert import ModernBertFineGrainedReward

# Initialize reward system with all 6 models
reward_system = ModernBertFineGrainedReward(
    tokenizer=policy_tokenizer,
    calculation_error_model_ckpt="models/prm_modernbert/calculation_error",
    fabrication_model_ckpt="models/prm_modernbert/fabrication",
    context_inconsistency_model_ckpt="models/prm_modernbert/context_inconsistency",
    factual_inconsistency_model_ckpt="models/prm_modernbert/factual_inconsistency",
    instruction_inconsistency_model_ckpt="models/prm_modernbert/instruction_inconsistency",
    logical_inconsistency_model_ckpt="models/prm_modernbert/logical_inconsistency",
    kl_coef=0.1
)

# Get rewards for generated text
rewards = reward_system.get_reward(
    prompts_input_ids, prompts_attention_mask,
    generated_input_ids, generated_attention_mask,
    generated_texts, metadata
)
```

## Verification

### Running Verification

```bash
# Verify solutions using trained ModernBERT PRMs
python src/verification/verify_modernbert.py \
    --model_base_dir models/prm_modernbert \
    --test_file data/test_data.json \
    --output_file verification_results.json
```

### Integration with Existing Pipeline

The ModernBERT verification system is designed to be compatible with the existing verification pipeline:

```python
from src.verification.verify_modernbert import ModernBertVerifier

# Initialize verifier
verifier = ModernBertVerifier("models/prm_modernbert")

# Verify solution candidates
question = "What is 2 + 2?"
candidates = ["2 + 2 = 4", "2 + 2 = 5"]
best_idx, results = verifier.verify_candidates(question, candidates)
```

## Key Features

### 1. Binary Classification per Error Type
- Each PRM is a binary classifier (error/no-error) for its specific error type
- More focused and interpretable than multi-class classification
- Allows for independent tuning of each error type

### 2. Aggregated Scoring
- During inference, scores from all 6 PRMs are summed across steps
- Compatible with existing "super-evaluator" aggregation logic
- Maintains the same interface as the original Longformer system

### 3. Step-wise Rewards
- Labels placed on [SEP] tokens before each step
- Same step-wise PRM loss as standard PRM training
- Preserves the process-level reward structure

### 4. ModernBERT Advantages
- More efficient than Longformer for sequence classification
- Better performance on token classification tasks
- Faster training and inference
- More recent architecture with improved capabilities

## Troubleshooting

### Common Issues

1. **CUDA Out of Memory**
   - Reduce `per_device_train_batch_size` in config
   - Increase `gradient_accumulation_steps`
   - Use `fp16: true` for mixed precision training

2. **Missing Training Data**
   - Ensure all 6 error type data files exist
   - Check file paths in configuration
   - Verify data format matches expected structure

3. **Model Loading Errors**
   - Check model paths are correct
   - Ensure models were saved properly during training
   - Verify tokenizer compatibility

### Performance Optimization

1. **Training Speed**
   - Use multiple GPUs with `--nproc_per_node`
   - Enable gradient checkpointing for memory efficiency
   - Use larger batch sizes with gradient accumulation

2. **Memory Usage**
   - Reduce `max_seq_length` if possible
   - Use `dataloader_num_workers: 0` to reduce memory overhead
   - Enable `fp16` or `bf16` training

## Expected Results

After successful training, you should have:

- 6 trained ModernBERT models, one for each error type
- Verification accuracy comparable to or better than Longformer baseline
- Faster inference due to ModernBERT efficiency
- More interpretable error-specific predictions

## Next Steps

1. **Evaluation**: Run verification on test sets to measure accuracy
2. **Fine-tuning**: Adjust hyperparameters based on validation results
3. **Integration**: Update downstream systems to use ModernBERT models
4. **Monitoring**: Set up production monitoring for model performance
