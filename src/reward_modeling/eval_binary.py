import evaluate
import datasets
import numpy as np
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, confusion_matrix

class BinaryClassificationMetric(evaluate.Metric):
    """Binary classification evaluation metric for ModernBERT reward models."""
    
    def _info(self):
        return evaluate.MetricInfo(
            description="Binary classification metrics for reward models",
            citation="",
            features=datasets.Features(
                {
                    "predictions": datasets.Value("int32"),
                    "references": datasets.Value("int32"),
                }
            )
        )

    def _compute(self, predictions, references):
        """
        Compute binary classification metrics.
        
        Args:
            predictions: List of predicted labels (0 or 1)
            references: List of true labels (0 or 1)
            
        Returns:
            Dictionary with accuracy, precision, recall, f1, and confusion matrix
        """
        predictions = np.array(predictions)
        references = np.array(references)
        
        # Basic accuracy
        accuracy = accuracy_score(references, predictions)
        
        # Precision, recall, F1 for both classes
        precision, recall, f1, support = precision_recall_fscore_support(
            references, predictions, average=None, labels=[0, 1]
        )
        
        # Macro and weighted averages
        precision_macro, recall_macro, f1_macro, _ = precision_recall_fscore_support(
            references, predictions, average='macro'
        )
        precision_weighted, recall_weighted, f1_weighted, _ = precision_recall_fscore_support(
            references, predictions, average='weighted'
        )
        
        # Confusion matrix
        cm = confusion_matrix(references, predictions, labels=[0, 1])
        tn, fp, fn, tp = cm.ravel()
        
        # Class-specific metrics
        no_error_precision = precision[0] if len(precision) > 0 else 0.0
        no_error_recall = recall[0] if len(recall) > 0 else 0.0
        no_error_f1 = f1[0] if len(f1) > 0 else 0.0
        no_error_support = support[0] if len(support) > 0 else 0
        
        has_error_precision = precision[1] if len(precision) > 1 else 0.0
        has_error_recall = recall[1] if len(recall) > 1 else 0.0
        has_error_f1 = f1[1] if len(f1) > 1 else 0.0
        has_error_support = support[1] if len(support) > 1 else 0
        
        return {
            # Overall metrics
            "accuracy": float(accuracy),
            "precision_macro": float(precision_macro),
            "recall_macro": float(recall_macro),
            "f1_macro": float(f1_macro),
            "precision_weighted": float(precision_weighted),
            "recall_weighted": float(recall_weighted),
            "f1_weighted": float(f1_weighted),
            
            # Class-specific metrics
            "no_error_precision": float(no_error_precision),
            "no_error_recall": float(no_error_recall),
            "no_error_f1": float(no_error_f1),
            "no_error_support": int(no_error_support),
            
            "has_error_precision": float(has_error_precision),
            "has_error_recall": float(has_error_recall),
            "has_error_f1": float(has_error_f1),
            "has_error_support": int(has_error_support),
            
            # Confusion matrix components
            "true_negatives": int(tn),
            "false_positives": int(fp),
            "false_negatives": int(fn),
            "true_positives": int(tp),
            
            # Total samples
            "total_samples": len(references),
        }


def convert_token_labels_to_binary(feedback_tags_list):
    """
    Convert token-level feedback tags to binary sequence-level labels.
    Handles comprehensive multi-error dataset with all 6 error types.

    Args:
        feedback_tags_list: List of lists, where each inner list contains
                           token-level feedback tags for one example

    Returns:
        List of binary labels (0 = no error, 1 = has error)
    """
    # Define all known error types from sixinone dataset
    ERROR_TYPES = {
        "Calculation-Error",
        "Context-Inconsistency",
        "Fabrication",
        "Factual-Inconsistency",
        "Instruction-Inconsistency",
        "Logical-Inconsistency",
        "F-ERR"  # Generic error tag
    }

    binary_labels = []
    for feedback_tags in feedback_tags_list:
        # Check if any token has an error tag (any of the 6+ error types)
        has_error = any(tag in ERROR_TYPES for tag in feedback_tags)
        binary_labels.append(1 if has_error else 0)
    return binary_labels


def evaluate_binary_predictions(predictions_file, references_file):
    """
    Evaluate binary classification predictions against references.
    
    Args:
        predictions_file: Path to file with predictions (one per line)
        references_file: Path to JSON file with reference data
        
    Returns:
        Dictionary with evaluation metrics
    """
    import json
    
    # Load predictions
    with open(predictions_file, 'r') as f:
        predictions = [int(line.strip().split('\t')[1]) for line in f if line.strip()]
    
    # Load references and convert to binary
    with open(references_file, 'r') as f:
        references_data = [json.loads(line) for line in f]
    
    feedback_tags_list = [item['feedback_tags'] for item in references_data]
    references = convert_token_labels_to_binary(feedback_tags_list)
    
    # Ensure same length
    min_len = min(len(predictions), len(references))
    predictions = predictions[:min_len]
    references = references[:min_len]
    
    # Compute metrics
    metric = BinaryClassificationMetric()
    results = metric.compute(predictions=predictions, references=references)
    
    return results


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) != 3:
        print("Usage: python eval_binary.py <predictions_file> <references_file>")
        sys.exit(1)
    
    predictions_file = sys.argv[1]
    references_file = sys.argv[2]
    
    results = evaluate_binary_predictions(predictions_file, references_file)
    
    print("Binary Classification Evaluation Results:")
    print("=" * 50)
    print(f"Accuracy: {results['accuracy']:.4f}")
    print(f"Macro F1: {results['f1_macro']:.4f}")
    print(f"Weighted F1: {results['f1_weighted']:.4f}")
    print()
    print("Class-specific metrics:")
    print(f"No Error - Precision: {results['no_error_precision']:.4f}, Recall: {results['no_error_recall']:.4f}, F1: {results['no_error_f1']:.4f}")
    print(f"Has Error - Precision: {results['has_error_precision']:.4f}, Recall: {results['has_error_recall']:.4f}, F1: {results['has_error_f1']:.4f}")
    print()
    print("Confusion Matrix:")
    print(f"True Negatives: {results['true_negatives']}")
    print(f"False Positives: {results['false_positives']}")
    print(f"False Negatives: {results['false_negatives']}")
    print(f"True Positives: {results['true_positives']}")
