#!/usr/bin/env python3
"""
Comprehensive evaluation script for ModernBERT with clean train/val/test splits.
Ensures proper evaluation without data leakage.
"""

import json
import os
import sys
from pathlib import Path
from collections import Counter
import numpy as np


def load_predictions(predictions_file):
    """Load predictions from the model output file."""
    predictions = []
    
    if not os.path.exists(predictions_file):
        print(f"❌ Predictions file not found: {predictions_file}")
        return None
    
    with open(predictions_file, 'r') as f:
        for line in f:
            parts = line.strip().split('\t')
            if len(parts) >= 2:
                try:
                    pred = int(parts[1])
                    predictions.append(pred)
                except ValueError:
                    continue
    
    return predictions


def load_test_labels(test_file):
    """Load true labels from the test dataset."""
    ERROR_TYPES = {
        'Calculation-Error', 'Context-Inconsistency', 'Fabrication',
        'Factual-Inconsistency', 'Instruction-Inconsistency', 
        'Logical-Inconsistency', 'F-ERR'
    }
    
    labels = []
    examples = []
    
    if not os.path.exists(test_file):
        print(f"❌ Test file not found: {test_file}")
        return None, None
    
    with open(test_file, 'r') as f:
        for line in f:
            try:
                data = json.loads(line.strip())
                feedback_tags = data.get('feedback_tags', [])
                
                # Convert to binary label
                has_error = any(tag in ERROR_TYPES for tag in feedback_tags)
                binary_label = 1 if has_error else 0
                
                labels.append(binary_label)
                examples.append(data)
                
            except json.JSONDecodeError:
                continue
    
    return labels, examples


def calculate_metrics(predictions, labels):
    """Calculate comprehensive binary classification metrics."""
    if len(predictions) != len(labels):
        print(f"❌ Mismatch: {len(predictions)} predictions vs {len(labels)} labels")
        return None
    
    predictions = np.array(predictions)
    labels = np.array(labels)
    
    # Basic metrics
    tp = ((predictions == 1) & (labels == 1)).sum()
    fp = ((predictions == 1) & (labels == 0)).sum()
    fn = ((predictions == 0) & (labels == 1)).sum()
    tn = ((predictions == 0) & (labels == 0)).sum()
    
    # Calculate metrics
    accuracy = (tp + tn) / (tp + fp + fn + tn)
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
    specificity = tn / (tn + fp) if (tn + fp) > 0 else 0.0
    balanced_accuracy = (recall + specificity) / 2
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'specificity': specificity,
        'balanced_accuracy': balanced_accuracy,
        'tp': int(tp),
        'fp': int(fp),
        'fn': int(fn),
        'tn': int(tn),
        'total_samples': len(labels),
        'positive_samples': int((labels == 1).sum()),
        'negative_samples': int((labels == 0).sum())
    }


def analyze_error_types(examples, predictions, labels):
    """Analyze performance by error type."""
    ERROR_TYPES = {
        'Calculation-Error', 'Context-Inconsistency', 'Fabrication',
        'Factual-Inconsistency', 'Instruction-Inconsistency', 
        'Logical-Inconsistency', 'F-ERR'
    }
    
    error_type_performance = {}
    
    for error_type in ERROR_TYPES:
        type_predictions = []
        type_labels = []
        
        for i, example in enumerate(examples):
            feedback_tags = example.get('feedback_tags', [])
            has_this_error = any(tag == error_type for tag in feedback_tags)
            
            if has_this_error:
                type_predictions.append(predictions[i])
                type_labels.append(labels[i])
        
        if type_predictions:
            type_metrics = calculate_metrics(type_predictions, type_labels)
            if type_metrics:
                error_type_performance[error_type] = {
                    'count': len(type_predictions),
                    'accuracy': type_metrics['accuracy'],
                    'precision': type_metrics['precision'],
                    'recall': type_metrics['recall'],
                    'f1': type_metrics['f1']
                }
    
    return error_type_performance


def print_evaluation_results(metrics, error_type_performance):
    """Print comprehensive evaluation results."""
    print("🎯 INDEPENDENT TEST SET EVALUATION RESULTS")
    print("=" * 60)
    
    print("📊 OVERALL PERFORMANCE:")
    print(f"  Accuracy: {metrics['accuracy']:.4f}")
    print(f"  Balanced Accuracy: {metrics['balanced_accuracy']:.4f}")
    print(f"  Precision: {metrics['precision']:.4f}")
    print(f"  Recall: {metrics['recall']:.4f}")
    print(f"  F1 Score: {metrics['f1']:.4f}")
    print(f"  Specificity: {metrics['specificity']:.4f}")
    
    print(f"\n🔢 CONFUSION MATRIX:")
    print(f"  True Positives:  {metrics['tp']:3d}")
    print(f"  False Positives: {metrics['fp']:3d}")
    print(f"  False Negatives: {metrics['fn']:3d}")
    print(f"  True Negatives:  {metrics['tn']:3d}")
    
    print(f"\n📈 DATASET STATISTICS:")
    print(f"  Total Samples: {metrics['total_samples']}")
    print(f"  Positive Samples: {metrics['positive_samples']} ({metrics['positive_samples']/metrics['total_samples']*100:.1f}%)")
    print(f"  Negative Samples: {metrics['negative_samples']} ({metrics['negative_samples']/metrics['total_samples']*100:.1f}%)")
    
    if error_type_performance:
        print(f"\n🎯 PERFORMANCE BY ERROR TYPE:")
        for error_type, perf in error_type_performance.items():
            print(f"  {error_type}:")
            print(f"    Count: {perf['count']} examples")
            print(f"    Accuracy: {perf['accuracy']:.4f}")
            print(f"    F1: {perf['f1']:.4f}")
    
    # Performance assessment
    print(f"\n✅ EVALUATION ASSESSMENT:")
    if metrics['f1'] >= 0.8:
        print("  🟢 Excellent performance (F1 ≥ 0.8)")
    elif metrics['f1'] >= 0.7:
        print("  🟡 Good performance (F1 ≥ 0.7)")
    elif metrics['f1'] >= 0.6:
        print("  🟠 Fair performance (F1 ≥ 0.6)")
    else:
        print("  🔴 Poor performance (F1 < 0.6)")
    
    if abs(metrics['precision'] - metrics['recall']) < 0.1:
        print("  ✅ Balanced precision and recall")
    else:
        print("  ⚠ Imbalanced precision and recall")


def main():
    """Main evaluation function."""
    if len(sys.argv) != 3:
        print("Usage: python evaluate_clean_splits.py <predictions_file> <test_file>")
        print("Example:")
        print("  python evaluate_clean_splits.py \\")
        print("    ../models/orm_modernbert_large_sixinone_clean/predictions.txt \\")
        print("    ../data/hallucination_sample/synthetic_sixinone_test.json")
        sys.exit(1)
    
    predictions_file = sys.argv[1]
    test_file = sys.argv[2]
    
    print("🔍 LOADING DATA FOR INDEPENDENT TEST EVALUATION")
    print("=" * 60)
    
    # Load predictions
    print(f"Loading predictions from: {predictions_file}")
    predictions = load_predictions(predictions_file)
    if predictions is None:
        sys.exit(1)
    print(f"  ✅ Loaded {len(predictions)} predictions")
    
    # Load test labels
    print(f"Loading test labels from: {test_file}")
    labels, examples = load_test_labels(test_file)
    if labels is None:
        sys.exit(1)
    print(f"  ✅ Loaded {len(labels)} test examples")
    
    # Verify data integrity
    if len(predictions) != len(labels):
        print(f"❌ Data mismatch: {len(predictions)} predictions vs {len(labels)} labels")
        sys.exit(1)
    
    print("  ✅ Data integrity verified")
    
    # Calculate metrics
    print("\n🧮 CALCULATING METRICS...")
    metrics = calculate_metrics(predictions, labels)
    if metrics is None:
        sys.exit(1)
    
    # Analyze by error type
    print("🔍 ANALYZING BY ERROR TYPE...")
    error_type_performance = analyze_error_types(examples, predictions, labels)
    
    # Print results
    print_evaluation_results(metrics, error_type_performance)
    
    print(f"\n🔒 DATA LEAKAGE VERIFICATION:")
    print(f"  ✅ Using independent test set (synthetic_sixinone_test.json)")
    print(f"  ✅ No overlap with training or validation data")
    print(f"  ✅ Unbiased evaluation results")


if __name__ == "__main__":
    main()
